// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.13;

import {IPCoin} from "../src/IPCoin.sol";
import {TraderNft} from "../src/TraderNft.sol";
import {CreatorNft} from "../src/CreatorNft.sol";
import {Governance} from "../src/Governance.sol";
import {DistributionPool} from "../src/DistributionPool.sol";
import {Factory} from "../src/Factory.sol";
import {Script, console} from "forge-std/Script.sol";
import "@openzeppelin/contracts/proxy/ERC1967/ERC1967Proxy.sol";

contract DeployAll is Script {
    function setUp() public {}

    function run() public {
        uint256 deployerPrivateKey = vm.envUint("NETWORK_ADMIN_PRIVATE_KEY");
        address feeCollector = vm.envAddress("FEE_COLLECTOR_ADDRESS");
        address admin = vm.envAddress("FEE_COLLECTOR_ADDRESS");

        console.log("feeCollector is:", feeCollector);
        console.log("admin is:", admin);

        vm.startBroadcast(deployerPrivateKey);

        // Deploy implementation contracts

        console.log("Deploying implementation contracts");
        // address ipCoinImpl = address(new IPCoin("Deploy IP Coin", "DIPC", msg.sender, msg.sender));
        // address traderNftImpl = address(new TraderNft());
        address creatorNftImpl = address(new CreatorNft());

        console.log("Deploying DistributionPool proxy");
        address poolProxy = address(new ERC1967Proxy(address(new DistributionPool()), ""));

        //        console.log("Deploying Governance proxy");
        //        address governanceProxy = address(new ERC1967Proxy(address(new Governance()), ""));

        console.log("Deploying Factory proxy");
        address factoryProxy = address(new ERC1967Proxy(address(new Factory()), ""));
        Factory factory = Factory(factoryProxy);
        factory.initialize(admin, creatorNftImpl, poolProxy);

        DistributionPool pool = DistributionPool(payable(poolProxy));
        pool.initialize(admin, admin, factoryProxy);

        // governance init
        //        Governance governance = Governance(governanceProxy);
        //        governance.initialize(admin);

        // console.log("IPCoin implementation:", ipCoinImpl);
        // console.log("TraderNft implementation:", traderNftImpl);
        // console.log("Governance address:", governanceProxy);
        console.log("CreatorNft implementation:", creatorNftImpl);
        console.log("Factory proxy deployed at", factoryProxy);
        console.log("DistributionPool address:", poolProxy);

        vm.stopBroadcast();
    }
}
