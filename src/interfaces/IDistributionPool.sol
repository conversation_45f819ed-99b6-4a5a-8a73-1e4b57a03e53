// SPDX-License-Identifier: MIT
pragma solidity ^0.8.26;

interface IDistributionPool {
    // Custom Errors
    error InsufficientAllowance();
    error InsufficientBalance();
    error TransferFailed();
    error UnsupportedChain();

    error InvalidAmount();
    error SlippageExceeded();

    error NotAuthorized();
    error FeatureDisabled();

    error MaxCapReached();
    error InsufficientLiquidity();
    error NoFeesAvailable();
    error MinimumTransactionLimit();

    error CoinAlreadyExists();
    error CoinNotExists();
    error RecipientRewardedThisInterval(); // Renamed error

    // Events
    event Buy(
        address indexed buyer,
        address recipient,
        address coin,
        uint256 tokenAmount,
        uint256 ethAmount,
        uint256 tokenId,
        bool withNft
    );
    event Sell(address indexed seller, address coin, uint256 tokenAmount, uint256 ethToReturn);
    event CoinAdded(address coin, address pair);
    event TraderNftAdded(address coin, address nft);
    event TokenWithdrawn(address user, address coin, uint256 tokenId, uint256 withdrawAmount);
    event TokenListedToDex(address coin, address pair);

    event PoolMigrated(address indexed coin, uint256 currentSupply, uint256 mintAmount, uint256 newVersion);

    event RewardDistributorAdded(address indexed distributor);
    event RewardDistributorRemoved(address indexed distributor);
    event WeeklyRewardLimitUpdated(uint256 newLimit);
    event RewardsDistributed(
        address indexed distributor, address indexed coin, uint256 totalAmount, uint256 recipientCount
    );
    event CreatorRewardsDistributed(address indexed distributor, address indexed coin, uint256 totalAmount);
    event WeeklyMiningReward(address indexed coin, uint256 weekNumber, uint256 rewardAmount, uint256 remainingSupply);
    event MaxWalletDistributionPercentageUpdated(uint256 newPercentage);
    event RewardIntervalUpdated(address indexed coin, uint256 newInterval);
    event GlobalRewardIntervalUpdated(uint256 newInterval);
    event CoinParametersUpdated(address indexed coin);
    event CoinDefaultRewardIntervalUpdated(address indexed coin, uint256 newInterval);
}
