// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

import {UUPSUpgradeable} from "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";
import {Initializable} from "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import {PausableUpgradeable} from "@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol";
import {ReentrancyGuardUpgradeable} from "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol";
import {AccessControlUpgradeable} from "@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol";
import {FixedPointMathLib} from "@solady/utils/FixedPointMathLib.sol";
import "./interfaces/IUniswapV2Router02.sol";
import "./interfaces/IUniswapV2Factory.sol";
import "./interfaces/IDistributionPool.sol";
import "./IPCoin.sol";
import "./TraderNft.sol";
import "./CreatorNft.sol";

/**
 * @title DistributionPool Contract
 * @notice This is a distribution pool contract for managing IP token trading and liquidity
 * @dev This contract implements token trading, liquidity management, and NFT interaction functionalities
 */
contract DistributionPool is
    Initializable,
    UUPSUpgradeable,
    AccessControlUpgradeable,
    ReentrancyGuardUpgradeable,
    PausableUpgradeable,
    IDistributionPool
{
    using FixedPointMathLib for uint256;

    // Role definitions
    bytes32 public constant ADMIN_ROLE = keccak256("ADMIN_ROLE");
    bytes32 public constant PAUSER_ROLE = keccak256("PAUSER_ROLE");
    bytes32 public constant ADDIP_ROLE = keccak256("ADDIP_ROLE");

    // System constants
    address private constant BlackHole = ******************************************;
    uint256 private constant SQRT_PRECISION = 1e9;
    uint256 private constant DEFAULT_PRECISION = 1e18;

    // System parameters
    uint256 public BONDING_CURVE_TRADE_CAP; // Maximum bonding curve token supply
    uint256 public PRICE_CONSTANT; // Price constant k
    uint256 private TRADE_FEE_RATIO; // Trading fee ratio
    uint256 private LIQUIDITY_TOKEN_AMOUNT; // Liquidity token amount
    uint256 private LIQUIDITY_NATIVE_AMOUNT; // Liquidity ETH/BNB amount
    uint256 public VIRTUAL_TOKEN_SUPPLY; // Virtual token supply
    uint256 public VIRTUAL_ETH_SUPPLY; // Virtual ETH supply
    uint256 public MINIMUM_ETH_TO_BUY; // Minimum ETH amount for buying

    // Address configurations
    address private FEE_RECEIVER; // Fee receiver address
    address private FACTORY_ADDR; // Factory contract address
    address private UNISWAP_FACTORY; // Uniswap factory address
    address private UNISWAP_ROUTER; // Uniswap router address

    // Maximum percentage of wallet balance that can be distributed (expressed in basis points)
    // 1000 basis points = 10%
    uint256 public MAX_WALLET_DISTRIBUTION_PERCENTAGE;

    // Exponential decay mining parameters
    uint256 public TOTAL_MINING_SUPPLY;
    uint256 public DECAY_CONSTANT;
    uint256 public DECAY_PRECISION;
    uint256 public INITIAL_WEEKLY_REWARD;

    uint256 private totalEthFee; // Total unclaimed ETH fees
    uint256 public currentVersion;

    // Global reward interval (default: 1 week)
    // @dev fixme 注意，这是一个全局的参数，测试时允许其为 1 会使 agent 获得分配的频率也解除控制
    uint256 public globalRewardInterval;

    // Main mapping for all coin pool data
    mapping(address => CoinPoolData) public coinPoolsData;

    // Mapping to track last reward received timestamp by recipient for a specific coin _coin -> recipient -> timestamp
    mapping(address => mapping(address => uint256)) public lastRewardReceivedTimestamp;

    // Whitelist for reward distribution
    mapping(address => bool) public creatorRewardDistributorsWhitelist;

    // Struct for reward distribution
    struct RewardDistribution {
        address recipient;
        uint256 amount;
    }

    // Coin-specific parameters struct
    struct CoinParameters {
        uint256 liquidityTokenAmount; // Liquidity token amount
        uint256 bondingCurveTradeCap; // Maximum bonding curve token supply
        uint256 tradeFeeRatio; // Trading fee ratio
        uint256 minimumEthToBuy; // Minimum ETH amount for buying
        uint256 maxWalletDistributionPercentage; // Maximum percentage of wallet balance that can be distributed
        uint256 defaultRewardInterval; // Default reward interval (from globalRewardInterval)
        uint256 totalMiningSupply; // Total mining supply
        uint256 decayConstant; // Decay constant for exponential decay mining
        uint256 decayPrecision; // Precision for decay constant
        uint256 initialWeeklyReward; // Initial weekly reward
        uint256 liquidityNativeAmount; // Liquidity ETH/BNB amount
        uint256 virtualEthSupply; // Virtual ETH supply
        uint256 virtualTokenSupply; // Virtual token supply
        uint256 priceConstant; // Price constant k
    }

    // Coin Pool Data Struct
    struct CoinPoolData {
        IPCoin ipCoinContract; // IP token contract
        TraderNft traderNftContract; // Trader NFT contract
        CreatorNft creatorNftContract; // Creator NFT contract
        bool allowTrade; // Token trading permission
        bool tradeNftStatus; // NFT trading status
        address pairAddress; // Token pair address
        address aiWallet; // AI wallet address
        uint256 poolVersion; // Pool version (previously ipVersions)
        uint256 tokenAmountInPool; // Token amount in pool (previously tokensAmounts)
        uint256 nativeAmountInPool; // Native token (ETH/BNB) amount in pool (previously coinsNativeAmount)
        uint256 intervalRewardDistributionTotal; // Interval reward distribution total
        uint256 epochCreatorRewardDistributionTotal; // Epoch creator reward distribution total
        uint256 lastDistributionTimestamp; // Last distribution timestamp
        uint256 lastCreatorDistributionTimestamp; // Last creator distribution timestamp
        uint256 cumulativeDistribution; // Cumulative distribution
        uint256 remainingSupply; // Remaining supply for exponential decay mining
        uint256 creationTimestamp; // Creation timestamp
        uint256 specificRewardInterval; // Specific reward interval (previously rewardIntervals)
        CoinParameters parameters; // Coin-specific parameters
    }

    /// @custom:oz-upgrades-unsafe-allow constructor
    constructor() {
        _disableInitializers();
    }

    /**
     * @notice Initialize the contract
     * @param _admin Admin address
     * @param _feeReceiver Fee receiver address
     * @param factory Factory contract address
     */
    function initialize(address _admin, address _feeReceiver, address factory) public initializer {
        __Pausable_init();
        __AccessControl_init();
        __ReentrancyGuard_init();
        __UUPSUpgradeable_init();

        _grantRole(ADMIN_ROLE, _admin);
        _grantRole(PAUSER_ROLE, _admin);
        _grantRole(ADDIP_ROLE, factory);

        FEE_RECEIVER = _feeReceiver;
        FACTORY_ADDR = factory;

        // 设置通用参数
        _setCommonParameters();

        // https://docs.base.org/docs/contracts/
        // https://developer.pancakeswap.finance/contracts/v2/factory-v2
        if (block.chainid == 8453) {
            // base mainnet
            UNISWAP_FACTORY = 0x8909Dc15e40173Ff4699343b6eB8132c65e18eC6;
            UNISWAP_ROUTER = 0x4752ba5DBc23f44D87826276BF6Fd6b1C372aD24;

            // 设置特定链的参数
        } else if (block.chainid == 84_532) {
            // base sepolia
            UNISWAP_FACTORY = 0x7Ae58f10f7849cA6F5fB71b7f45CB416c9204b1e;
            UNISWAP_ROUTER = 0x1689E7B1F10000AE47eBfE339a4f69dECd19F602;

            // 设置特定链的参数
        } else if (block.chainid == 97) {
            // bsc testnet
            UNISWAP_FACTORY = 0x6725F303b657a9451d8BA641348b6761A6CC7a17;
            UNISWAP_ROUTER = 0xD99D1c33F9fC3444f8101754aBC46c52416550D1;

            // 设置特定链的参数
        } else if (block.chainid == 56) {
            // bsc mainnet
            UNISWAP_FACTORY = 0xcA143Ce32Fe78f1f7019d7d551a6402fC5350c73;
            UNISWAP_ROUTER = 0x10ED43C718714eb63d5aA57B78B54704E256024E;
        } else {
            revert UnsupportedChain();
        }

        // 设置特定链的参数
        _setChainSpecificParameters();
    }

    // ------------------------ User Functions -----------------------------------

    /**
     * @notice Buy tokens
     * @param _coin Token address
     * @param minTokensToBuy Minimum amount of tokens to buy
     */
    function buy(
        address _coin,
        address recipient,
        uint256 minTokensToBuy
    ) external payable whenNotPaused nonReentrant {
        CoinPoolData storage poolData = coinPoolsData[_coin];

        // Pre-buy checks
        require(poolData.allowTrade, FeatureDisabled());
        require(poolData.tokenAmountInPool <= poolData.parameters.bondingCurveTradeCap, MaxCapReached());
        require(msg.value >= poolData.parameters.minimumEthToBuy, MinimumTransactionLimit());
        require(recipient != address(this), "Invalid recipient");

        // Calculate token amount to buy
        uint256 tokensToBuy = calculateTokenAmount(_coin, msg.value);
        // Confirm token amount is valid
        require(tokensToBuy > 0, InvalidAmount());
        require(tokensToBuy >= minTokensToBuy, SlippageExceeded());
        require(poolData.tokenAmountInPool + tokensToBuy <= poolData.parameters.bondingCurveTradeCap, MaxCapReached());

        // Calculate buyer fee
        uint256 buyerFee = FixedPointMathLib.mulWad(msg.value, poolData.parameters.tradeFeeRatio);

        // Update token amounts
        poolData.tokenAmountInPool = poolData.tokenAmountInPool + tokensToBuy;
        poolData.nativeAmountInPool = poolData.nativeAmountInPool + msg.value - buyerFee;
        totalEthFee = totalEthFee + buyerFee;

        // Store token ID before potential state changes
        uint256 tokenId = 0;

        // Disable trading if bonding curve is 100% complete
        if (poolData.tokenAmountInPool >= poolData.parameters.bondingCurveTradeCap) {
            addLiquidityToPool(_coin);
        }

        // If Trader NFT is enabled, mint token and record transaction
        if (poolData.tradeNftStatus) {
            tokenId = poolData.traderNftContract.safeMint(recipient, tokensToBuy);
        } else {
            // Use safe transfer pattern to prevent reentrancy
            require(poolData.ipCoinContract.transfer(recipient, tokensToBuy), TransferFailed());
        }

        emit Buy(msg.sender, recipient, _coin, tokensToBuy, msg.value, tokenId, poolData.tradeNftStatus);
    }

    /**
     * @notice Sell tokens
     * @param _coin Token address
     * @param tokenAmount Amount of tokens to sell
     * @param minEthToReturn Minimum ETH amount to return
     */
    function sell(address _coin, uint256 tokenAmount, uint256 minEthToReturn) external whenNotPaused nonReentrant {
        CoinPoolData storage poolData = coinPoolsData[_coin];

        // Pre-sell checks
        require(poolData.allowTrade, FeatureDisabled());
        require(tokenAmount > 0, InvalidAmount());
        // todo: 可以看看这个限制能否去除，但还是要以安全性为第一优先考虑
        require(tokenAmount > SQRT_PRECISION, InvalidAmount());

        uint256 userCoinBalance = poolData.ipCoinContract.balanceOf(msg.sender);
        require(userCoinBalance >= tokenAmount, InsufficientBalance());

        uint256 allowance = poolData.ipCoinContract.allowance(msg.sender, address(this));
        require(allowance >= tokenAmount, InsufficientAllowance());

        (uint256 ethToReturn, uint256 sellerFee) = _calculateETHAmount(_coin, tokenAmount);

        // Confirm ETH amount to return is valid
        require(ethToReturn >= minEthToReturn, SlippageExceeded());
        require(poolData.nativeAmountInPool > ethToReturn, InvalidAmount());
        require(address(this).balance > ethToReturn, InvalidAmount());

        // Update token amounts
        poolData.tokenAmountInPool = poolData.tokenAmountInPool - tokenAmount;
        poolData.nativeAmountInPool = poolData.nativeAmountInPool - (ethToReturn + sellerFee);
        totalEthFee = totalEthFee + sellerFee;

        // transfer form to pool
        require(poolData.ipCoinContract.transferFrom(msg.sender, address(this), tokenAmount), TransferFailed());

        // Transfer ETH to seller
        (bool success,) = payable(msg.sender).call{value: ethToReturn}("");
        require(success, TransferFailed());

        emit Sell(_coin, msg.sender, tokenAmount, ethToReturn);
    }

    /**
     * @notice Add liquidity to Uniswap pool
     * @param _coin Token address
     * @return Liquidity pool address
     */
    function addLiquidityToPool(
        address _coin
    ) internal whenNotPaused returns (address) {
        CoinPoolData storage poolData = coinPoolsData[_coin];

        // Pre-liquidity checks
        require(address(poolData.ipCoinContract) != address(0), CoinNotExists());
        require(poolData.allowTrade, FeatureDisabled());
        require(poolData.nativeAmountInPool >= poolData.parameters.liquidityNativeAmount, InsufficientLiquidity());
        require(address(this).balance >= poolData.parameters.liquidityNativeAmount, InsufficientBalance());
        require(poolData.tokenAmountInPool == poolData.parameters.bondingCurveTradeCap, InsufficientBalance());

        // Set token as listed BEFORE adding liquidity to allow transfers to pair
        poolData.ipCoinContract.setListed();

        // Mint tokens for liquidity
        poolData.ipCoinContract.approve(UNISWAP_ROUTER, poolData.parameters.liquidityTokenAmount);
        IUniswapV2Router02 router = IUniswapV2Router02(UNISWAP_ROUTER);

        // Add liquidity with ETH
        router.addLiquidityETH{value: poolData.parameters.liquidityNativeAmount}(
            _coin,
            poolData.parameters.liquidityTokenAmount,
            poolData.parameters.liquidityTokenAmount, // slippage is unavoidable
            poolData.parameters.liquidityNativeAmount, // slippage is unavoidable
            BlackHole,
            block.timestamp + 300
        );

        poolData.allowTrade = false;
        // Collect listing fee (approximately 0.2 ETH)
        // @dev When the Bonding curve reaches 100%, the part that exceeds the expected number of eth is the part of the deposit fee
        totalEthFee = totalEthFee + (poolData.nativeAmountInPool - poolData.parameters.liquidityNativeAmount);
        // Update token amounts
        poolData.nativeAmountInPool = 0;
        poolData.tokenAmountInPool = poolData.tokenAmountInPool + poolData.parameters.liquidityTokenAmount;

        emit TokenListedToDex(_coin, poolData.pairAddress);

        return poolData.pairAddress;
    }

    // ------------------------ Bonding Curve Compute Logic -----------------------------------

    /**
     * @notice Calculate token amount for given ETH amount
     * @dev Uses Bonding Curve formula, considering fees and maximum supply
     * @param _coin Token address
     * @param ethAmount ETH amount
     * @return Token amount
     */
    function calculateTokenAmount(address _coin, uint256 ethAmount) public view returns (uint256) {
        CoinPoolData storage poolData = coinPoolsData[_coin];
        require(address(poolData.ipCoinContract) != address(0), CoinNotExists());

        if (ethAmount == 0) return 0;
        if (!poolData.allowTrade) {
            return 0;
        }

        uint256 buyerFee = FixedPointMathLib.mulWad(ethAmount, poolData.parameters.tradeFeeRatio);

        ethAmount = ethAmount - buyerFee;

        uint256 ethAmountWithVirtual = poolData.nativeAmountInPool + poolData.parameters.virtualEthSupply;

        // Calculate raw token amount
        uint256 rawTokenAmount = _rawCalculateTokens(ethAmountWithVirtual, ethAmount, poolData.parameters.priceConstant);

        // Check max token cap
        if (rawTokenAmount + poolData.tokenAmountInPool > poolData.parameters.bondingCurveTradeCap) {
            rawTokenAmount = poolData.parameters.bondingCurveTradeCap - poolData.tokenAmountInPool;
        }

        return rawTokenAmount;
    }

    /**
     * @notice Calculate ETH amount for given token amount
     * @dev Internal function, uses Bonding Curve formula, considering fees and pool balance
     * @param _coin Token address
     * @param tokenAmount Token amount
     * @return finalAmount Final ETH amount
     * @return fee Fee amount
     */
    function _calculateETHAmount(address _coin, uint256 tokenAmount) private view returns (uint256, uint256) {
        CoinPoolData storage poolData = coinPoolsData[_coin];

        if (tokenAmount == 0) return (0, 0);
        if (!poolData.allowTrade) {
            return (0, 0);
        }
        if (poolData.tokenAmountInPool < tokenAmount) revert InsufficientBalance();

        uint256 tokenAmountWithVirtual = poolData.tokenAmountInPool + poolData.parameters.virtualTokenSupply;

        // Calculate raw ETH amount
        uint256 rawEthAmount = _rawCalculateEth(tokenAmountWithVirtual, tokenAmount, poolData.parameters.priceConstant);

        // Calculate fee
        uint256 fee = FixedPointMathLib.mulWad(rawEthAmount, poolData.parameters.tradeFeeRatio);
        // uint256 fee = (rawEthAmount * 3) / 100;
        // Calculate final amount
        uint256 finalAmount = rawEthAmount - fee;

        // Ensure we don't exceed pool's ETH balance
        if (finalAmount >= poolData.nativeAmountInPool) {
            // This branch handles an extreme fallback scenario where the finalAmount
            // computed by the bonding curve exceeds the current coinEth balance.
            // Under normal circumstances, this should never happen if all parameters
            // are correctly scaled and the pool balance is maintained.
            // If this occurs, we recalculate the payout and fee based on the current
            // pool balance to prevent over-withdrawal of ETH.
            if (tokenAmount < poolData.tokenAmountInPool) {
                revert InsufficientBalance();
            }
            fee = FixedPointMathLib.mulWad(poolData.nativeAmountInPool, poolData.parameters.tradeFeeRatio);
            finalAmount = poolData.nativeAmountInPool - fee;
        }

        return (finalAmount, fee);
    }

    /**
     * @notice Calculate raw token amount using Bonding Curve
     * @dev Uses square root formula
     * @param currentEthAmount Current ETH amount
     * @param ethAmount ETH amount
     * @param priceConstant Price constant for the calculation
     * @return Token amount
     */
    function _rawCalculateTokens(
        uint256 currentEthAmount,
        uint256 ethAmount,
        uint256 priceConstant
    ) private pure returns (uint256) {
        uint256 sqrtAfter = FixedPointMathLib.sqrtWad(currentEthAmount + ethAmount);
        uint256 sqrtBefore = FixedPointMathLib.sqrtWad(currentEthAmount);

        uint256 difference = sqrtAfter - sqrtBefore;
        uint256 divided = FixedPointMathLib.divWad(difference, priceConstant);

        return 2 * divided;
    }

    /**
     * @notice Calculate raw ETH amount using Bonding Curve
     * @dev Uses quadratic equation
     * @param currentTokens Current token amount
     * @param tokenAmount Token amount
     * @param priceConstant Price constant for the calculation
     * @return ETH amount
     */
    function _rawCalculateEth(
        uint256 currentTokens,
        uint256 tokenAmount,
        uint256 priceConstant
    ) private pure returns (uint256) {
        uint256 meiotic = 2 * FixedPointMathLib.mulWad(currentTokens, tokenAmount);
        uint256 minuend = FixedPointMathLib.mulWad(tokenAmount, tokenAmount);
        uint256 afterSub = meiotic - minuend;
        uint256 waitDiv1 = FixedPointMathLib.mulWad(afterSub, priceConstant);
        uint256 dividend = FixedPointMathLib.mulWad(waitDiv1, priceConstant);
        uint256 result = dividend / 4;

        return result;
    }

    // ------------------------ Public Query Functions -----------------------------------

    /**
     * @notice Calculate ETH amount for given token amount
     * @dev Public query function, returns ETH amount considering fees
     * @param _coin Token address
     * @param tokenAmount Token amount
     * @return ETH amount
     */
    function calculateEthAmount(address _coin, uint256 tokenAmount) public view returns (uint256) {
        CoinPoolData storage poolData = coinPoolsData[_coin];
        require(address(poolData.ipCoinContract) != address(0), CoinNotExists());

        if (!poolData.allowTrade) {
            return 0;
        }
        (uint256 ethAmount,) = _calculateETHAmount(_coin, tokenAmount);
        return ethAmount;
    }

    /**
     * @notice Get token's ETH amount
     * @param _coin Token address
     * @return Token's ETH amount
     */
    function getCoinEthAmount(
        address _coin
    ) external view returns (uint256) {
        return coinPoolsData[_coin].nativeAmountInPool;
    }

    /**
     * @notice Get token's trading status
     * @param _coin Token address
     * @return Token's trading status
     */
    function getCoinTradeStatus(
        address _coin
    ) external view returns (bool) {
        return coinPoolsData[_coin].allowTrade;
    }

    /**
     * @notice Get token's total amount
     * @param _coin Token address
     * @return Token's total amount
     */
    function getCoinTokenAmount(
        address _coin
    ) external view returns (uint256) {
        return coinPoolsData[_coin].tokenAmountInPool;
    }

    /**
     * @notice Get token's capacity and supply
     * @dev Returns total capacity and supply, including virtual supply
     * @param _coin Token address
     * @return Token's capacity and supply
     */
    function getCapAndSupply(
        address _coin
    ) external view returns (uint256, uint256) {
        CoinPoolData storage poolData = coinPoolsData[_coin];
        require(address(poolData.ipCoinContract) != address(0), CoinNotExists());

        if (!poolData.allowTrade) {
            return (poolData.parameters.liquidityNativeAmount, poolData.ipCoinContract.totalSupply());
        }
        return
            (poolData.nativeAmountInPool + poolData.parameters.virtualEthSupply, poolData.ipCoinContract.totalSupply());
    }

    /**
     * @notice Get fee receiver address
     * @return Fee receiver address
     */
    function getFeeReceiver() external view returns (address) {
        return FEE_RECEIVER;
    }

    /**
     * @notice Get total ETH fees
     * @return Total ETH fees
     */
    function getTotalEthFee() external view returns (uint256) {
        return totalEthFee;
    }

    /**
     * @notice Get current week number for a specific coin pool
     * @dev Calculates the current week number based on pool creation timestamp
     * @param _coin IPCoin address
     * @return currentWeekNumber Current week number (1-based)
     */
    function getCurrentWeekNumber(
        address _coin
    ) external view returns (uint256) {
        CoinPoolData storage poolData = coinPoolsData[_coin];

        // Check if IPCoin exists
        require(address(poolData.ipCoinContract) != address(0), CoinNotExists());

        // Handle legacy data migration - if creationTimestamp is not set or too small (possibly legacy data)
        uint256 creationTimestamp = poolData.creationTimestamp;
        if (creationTimestamp <= 100) {
            creationTimestamp = block.timestamp;
        }

        // Calculate current week number using partial first week + calendar week approach
        // Calculate which day of the week the creation time is (0=Monday, 6=Sunday)
        uint256 creationWeekday = (creationTimestamp / 86_400 + 4) % 7; // 0-6, 0 is Monday

        // Calculate the end time of the creation week's Sunday
        uint256 daysUntilSunday = (6 - creationWeekday); // Days until Sunday
        uint256 endOfFirstWeek = (creationTimestamp / 86_400) * 86_400 + daysUntilSunday * 86_400 + 86_399; // Sunday 23:59:59

        if (block.timestamp <= endOfFirstWeek) {
            // Still in the first week (partial week)
            return 1;
        } else {
            // Past the first week, calculate subsequent full weeks
            uint256 timeAfterFirstWeek = block.timestamp - endOfFirstWeek - 1;
            uint256 fullWeeksPassed = timeAfterFirstWeek / 604_800; // Full weeks
            return 2 + fullWeeksPassed; // First week + passed full weeks
        }
    }

    /**
     * @notice Check if account has admin role
     * @param account Account address
     * @return If account has admin role
     */
    function checkAdmin(
        address account
    ) external view returns (bool) {
        return hasRole(ADMIN_ROLE, account);
    }

    /**
     * @notice Set maximum percentage of wallet balance that can be distributed
     * @dev Only callable by admin role
     * @param percentage New maximum percentage in basis points (100 = 1%)
     */
    function setMaxWalletDistributionPercentage(
        uint256 percentage
    ) external onlyRole(ADMIN_ROLE) {
        require(percentage > 0 && percentage <= 10_000, "Invalid percentage"); // Max 100% (10000 basis points)
        MAX_WALLET_DISTRIBUTION_PERCENTAGE = percentage;
        emit MaxWalletDistributionPercentageUpdated(percentage);
    }

    /**
     * @notice Update coin-specific parameters
     * @dev Only callable by admin role
     * @param _coin Token address
     * @param liquidityTokenAmount Liquidity token amount
     * @param bondingCurveTradeCap Maximum bonding curve token supply
     * @param tradeFeeRatio Trading fee ratio
     * @param minimumEthToBuy Minimum ETH amount for buying
     * @param maxWalletDistributionPercentage Maximum percentage of wallet balance that can be distributed
     * @param defaultRewardInterval Default reward interval
     * @param totalMiningSupply Total mining supply
     * @param decayConstant Decay constant for exponential decay mining
     * @param decayPrecision Precision for decay constant
     * @param initialWeeklyReward Initial weekly reward
     * @param liquidityNativeAmount Liquidity ETH/BNB amount
     * @param virtualEthSupply Virtual ETH supply
     * @param virtualTokenSupply Virtual token supply
     * @param priceConstant Price constant k
     */
    function setCoinParameters(
        address _coin,
        uint256 liquidityTokenAmount,
        uint256 bondingCurveTradeCap,
        uint256 tradeFeeRatio,
        uint256 minimumEthToBuy,
        uint256 maxWalletDistributionPercentage,
        uint256 defaultRewardInterval,
        uint256 totalMiningSupply,
        uint256 decayConstant,
        uint256 decayPrecision,
        uint256 initialWeeklyReward,
        uint256 liquidityNativeAmount,
        uint256 virtualEthSupply,
        uint256 virtualTokenSupply,
        uint256 priceConstant
    ) external onlyRole(ADMIN_ROLE) {
        CoinPoolData storage poolData = coinPoolsData[_coin];
        require(address(poolData.ipCoinContract) != address(0), CoinNotExists());

        poolData.parameters.liquidityTokenAmount = liquidityTokenAmount;
        poolData.parameters.bondingCurveTradeCap = bondingCurveTradeCap;
        poolData.parameters.tradeFeeRatio = tradeFeeRatio;
        poolData.parameters.minimumEthToBuy = minimumEthToBuy;
        poolData.parameters.maxWalletDistributionPercentage = maxWalletDistributionPercentage;
        poolData.parameters.defaultRewardInterval = defaultRewardInterval;
        poolData.parameters.totalMiningSupply = totalMiningSupply;
        poolData.parameters.decayConstant = decayConstant;
        poolData.parameters.decayPrecision = decayPrecision;
        poolData.parameters.initialWeeklyReward = initialWeeklyReward;
        poolData.parameters.liquidityNativeAmount = liquidityNativeAmount;
        poolData.parameters.virtualEthSupply = virtualEthSupply;
        poolData.parameters.virtualTokenSupply = virtualTokenSupply;
        poolData.parameters.priceConstant = priceConstant;

        emit CoinParametersUpdated(_coin);
    }

    /**
     * @notice Update coin-specific default reward interval
     * @dev Only callable by admin role
     * @param _coin Token address
     * @param defaultRewardInterval New default reward interval
     */
    function setCoinDefaultRewardInterval(address _coin, uint256 defaultRewardInterval) external onlyRole(ADMIN_ROLE) {
        CoinPoolData storage poolData = coinPoolsData[_coin];
        require(address(poolData.ipCoinContract) != address(0), CoinNotExists());

        poolData.parameters.defaultRewardInterval = defaultRewardInterval;
        emit CoinDefaultRewardIntervalUpdated(_coin, defaultRewardInterval);
    }

    // ------------------------ Admin Functions -----------------------------------
    /**
     * @notice Add IP token
     * @dev Only callable by ADDIP_ROLE
     * @param _coin Token address
     */
    function addIp(address _coin, address _creatorNft, address _aiAgentWallet) external onlyRole(ADDIP_ROLE) {
        CoinPoolData storage poolData = coinPoolsData[_coin];
        require(address(poolData.ipCoinContract) == address(0), CoinAlreadyExists());

        poolData.ipCoinContract = IPCoin(_coin);
        poolData.tokenAmountInPool = 0;
        poolData.nativeAmountInPool = 0;
        poolData.allowTrade = true;
        poolData.tradeNftStatus = false;
        poolData.creatorNftContract = CreatorNft(_creatorNft);
        poolData.aiWallet = _aiAgentWallet;
        poolData.poolVersion = currentVersion;
        poolData.creationTimestamp = block.timestamp;
        poolData.parameters.liquidityTokenAmount = LIQUIDITY_TOKEN_AMOUNT;
        poolData.parameters.bondingCurveTradeCap = BONDING_CURVE_TRADE_CAP;
        poolData.parameters.tradeFeeRatio = TRADE_FEE_RATIO;
        poolData.parameters.minimumEthToBuy = MINIMUM_ETH_TO_BUY;
        poolData.parameters.maxWalletDistributionPercentage = MAX_WALLET_DISTRIBUTION_PERCENTAGE;
        poolData.parameters.defaultRewardInterval = globalRewardInterval;
        poolData.parameters.totalMiningSupply = TOTAL_MINING_SUPPLY;
        poolData.parameters.decayConstant = DECAY_CONSTANT;
        poolData.parameters.decayPrecision = DECAY_PRECISION;
        poolData.parameters.initialWeeklyReward = INITIAL_WEEKLY_REWARD;
        poolData.parameters.liquidityNativeAmount = LIQUIDITY_NATIVE_AMOUNT;
        poolData.parameters.virtualEthSupply = VIRTUAL_ETH_SUPPLY;
        poolData.parameters.virtualTokenSupply = VIRTUAL_TOKEN_SUPPLY;
        poolData.parameters.priceConstant = PRICE_CONSTANT;

        // 初始化指数衰减挖矿参数
        poolData.remainingSupply = poolData.parameters.totalMiningSupply;

        address pair = IUniswapV2Factory(UNISWAP_FACTORY).createPair(_coin, IUniswapV2Router02(UNISWAP_ROUTER).WETH());
        poolData.pairAddress = pair;

        poolData.ipCoinContract.mint(address(this), 1_000_000_000 * DEFAULT_PRECISION);

        poolData.ipCoinContract.initPair(pair);

        emit CoinAdded(_coin, pair);
    }

    /**
     * @notice Set token's NFT status
     * @dev Only callable by ADMIN_ROLE
     * @param _coin Token address
     * @param _nft NFT contract address
     */
    function addTraderNft(address _coin, address _nft) external onlyRole(ADMIN_ROLE) {
        CoinPoolData storage poolData = coinPoolsData[_coin];
        require(address(poolData.ipCoinContract) != address(0), CoinNotExists());

        poolData.traderNftContract = TraderNft(_nft);
        poolData.tradeNftStatus = true;

        emit TraderNftAdded(_coin, _nft);
    }

    /**
     * @notice Set fee receiver address
     * @dev Only callable by ADMIN_ROLE
     * @param _feeReceiver Fee receiver address
     */
    function setFeeReceiver(
        address _feeReceiver
    ) external onlyRole(ADMIN_ROLE) {
        FEE_RECEIVER = _feeReceiver;
    }

    /**
     * @notice Withdraw ETH fees
     * @dev Only callable by ADMIN_ROLE
     */
    function withdrawFee() external onlyRole(ADMIN_ROLE) nonReentrant {
        require(totalEthFee > 0, NoFeesAvailable());

        (bool success,) = payable(FEE_RECEIVER).call{value: totalEthFee}("");
        require(success, TransferFailed());

        totalEthFee = 0;
    }

    /**
     * @notice Update factory contract address
     * @dev Only callable by ADMIN_ROLE
     * @param factory New factory contract address
     */
    function setFactory(
        address factory
    ) external onlyRole(ADMIN_ROLE) {
        require(factory != address(0), "Factory address cannot be zero");
        if (hasRole(ADDIP_ROLE, FACTORY_ADDR)) {
            _revokeRole(ADDIP_ROLE, FACTORY_ADDR);
        }
        FACTORY_ADDR = factory;
        _grantRole(ADDIP_ROLE, FACTORY_ADDR);
    }

    function removeAdmin(
        address _oldAdmin
    ) external onlyRole(ADMIN_ROLE) {
        require(_oldAdmin != address(0), "Invalid admin address");
        require(_oldAdmin != msg.sender, "You cannot remove yourself");

        _revokeRole(ADMIN_ROLE, _oldAdmin);
    }

    function addAdmin(
        address _newAdmin
    ) external onlyRole(ADMIN_ROLE) {
        _grantRole(ADMIN_ROLE, _newAdmin);
    }

    function addPauser(
        address _newPauser
    ) external onlyRole(ADMIN_ROLE) {
        _grantRole(PAUSER_ROLE, _newPauser);
    }

    function removePauser(
        address _oldPauser
    ) external onlyRole(ADMIN_ROLE) {
        _revokeRole(PAUSER_ROLE, _oldPauser);
    }

    /**
     * @notice Set minimum ETH amount for buying
     * @dev Only callable by ADMIN_ROLE
     * @param _minimumToBuy Minimum ETH amount for buying
     */
    function setMinimumToBuy(
        uint256 _minimumToBuy
    ) external onlyRole(ADMIN_ROLE) {
        MINIMUM_ETH_TO_BUY = _minimumToBuy;
    }

    /**
     * @notice Set liquidity ETH amount
     * @dev Only callable by ADMIN_ROLE
     * @param _liquidityEthAmount Liquidity ETH amount
     */
    function setLiquidityEthAmount(
        uint256 _liquidityEthAmount
    ) external onlyRole(ADMIN_ROLE) {
        LIQUIDITY_NATIVE_AMOUNT = _liquidityEthAmount;
    }

    /**
     * @notice Pause contract
     * @dev Only callable by PAUSER_ROLE
     */
    function pause() external onlyRole(PAUSER_ROLE) {
        _pause();
    }

    /**
     * @notice Unpause contract
     * @dev Only callable by PAUSER_ROLE
     */
    function unpause() external onlyRole(PAUSER_ROLE) {
        _unpause();
    }

    function migrateNewVersion() external onlyRole(ADMIN_ROLE) {
        uint256 newVersion = 2_025_060_501;

        if (currentVersion >= newVersion) {
            revert("Already migrated to new version");
        }

        // 设置通用参数
        _setCommonParameters();

        currentVersion = newVersion;

        // 设置特定链的参数
        _setChainSpecificParameters();
    }

    // ------------------------ Reward Distribution Functions -----------------------------------

    /**
     * @notice Add an address to the reward distributors whitelist
     * @dev Only callable by ADMIN_ROLE
     * @param distributor Address to add to whitelist
     */
    function addRewardDistributor(
        address distributor
    ) external onlyRole(ADMIN_ROLE) {
        creatorRewardDistributorsWhitelist[distributor] = true;
        emit RewardDistributorAdded(distributor);
    }

    /**
     * @notice Remove an address from the reward distributors whitelist
     * @dev Only callable by ADMIN_ROLE
     * @param distributor Address to remove from whitelist
     */
    function removeRewardDistributor(
        address distributor
    ) external onlyRole(ADMIN_ROLE) {
        creatorRewardDistributorsWhitelist[distributor] = false;
        emit RewardDistributorRemoved(distributor);
    }

    /**
     * @notice Set reward interval for a specific coin
     * @dev Only callable by ADMIN_ROLE, when set to 0 it will use the global interval
     * @param _coin IPCoin address
     * @param interval New reward interval in seconds, or 0 to use global interval
     */
    function setRewardInterval(address _coin, uint256 interval) external onlyRole(ADMIN_ROLE) {
        CoinPoolData storage poolData = coinPoolsData[_coin];
        require(address(poolData.ipCoinContract) != address(0), CoinNotExists());
        poolData.specificRewardInterval = interval;
        emit RewardIntervalUpdated(_coin, interval);
    }

    /**
     * @notice Get reward interval for a specific coin
     * @param _coin IPCoin address
     * @return Reward interval in seconds
     */
    function getRewardInterval(
        address _coin
    ) external view returns (uint256) {
        CoinPoolData storage poolData = coinPoolsData[_coin];
        require(address(poolData.ipCoinContract) != address(0), CoinNotExists());
        return poolData.specificRewardInterval;
    }

    /**
     * @notice Set the global reward interval that applies to all coins without specific intervals
     * @dev Only callable by ADMIN_ROLE
     * @param interval New global reward interval in seconds
     */
    function setGlobalRewardInterval(
        uint256 interval
    ) external onlyRole(ADMIN_ROLE) {
        require(interval > 0, "Interval must be greater than zero");
        globalRewardInterval = interval;
        emit GlobalRewardIntervalUpdated(interval);
    }

    /**
     * @notice Get the global reward interval that applies to all coins without specific intervals
     * @return Global reward interval in seconds
     */
    function getGlobalRewardInterval() external view returns (uint256) {
        return globalRewardInterval;
    }

    /**
     * @notice Get the weekly output limit for a specific coin and week number
     * @dev Calculates the maximum weekly output based on exponential decay algorithm
     * @dev Formula: Rw = R1 × (1-k)^(w-1) where R1 is initial weekly reward, k is decay constant
     * @dev Alternative: Rw = k × Sw-1 where Sw-1 = S0 × (1-k)^(w-1) is remaining supply at week start
     * @param _coin IPCoin address
     * @param weekNumber Week number (1-based) to query the output limit for
     * @return weeklyOutputLimit Maximum output limit for the specified week
     */
    function getWeeklyOutputLimit(
        address _coin,
        uint256 weekNumber
    ) external view returns (uint256 weeklyOutputLimit) {
        CoinPoolData storage poolData = coinPoolsData[_coin];

        // Check if IPCoin exists
        require(address(poolData.ipCoinContract) != address(0), CoinNotExists());
        require(weekNumber > 0, "Week number must be greater than 0");

        // Get decay parameters
        uint256 decayConstant = poolData.parameters.decayConstant; // k (e.g., 5530 for 0.00553)
        uint256 decayPrecision = poolData.parameters.decayPrecision; // precision (e.g., 1,000,000)
        uint256 totalMiningSupply = poolData.parameters.totalMiningSupply; // S0 (initial supply)

        // Calculate using the closed-form formula: Rw = R1 × (1-k)^(w-1)
        // Where R1 = k × S0 (initial weekly reward)

        // Calculate (1-k) with precision
        // (1-k) = (decayPrecision - decayConstant) / decayPrecision
        uint256 decayFactor = decayPrecision - decayConstant; // (1-k) × precision

        // Calculate (1-k)^(w-1) using power function
        uint256 decayPower = _power(decayFactor, decayPrecision, weekNumber - 1);

        // Calculate R1 = k × S0
        uint256 initialWeeklyReward = (totalMiningSupply * decayConstant) / decayPrecision;

        // Calculate Rw = R1 × (1-k)^(w-1)
        weeklyOutputLimit = (initialWeeklyReward * decayPower) / decayPrecision;

        return weeklyOutputLimit;
    }

    /**
     * @notice Calculate base^exponent with precision handling for decay calculations
     * @dev Uses iterative multiplication to calculate power, optimized for small exponents
     * @param base Base value (with precision)
     * @param precision Precision factor
     * @param exponent Exponent value
     * @return result Base^exponent with precision
     */
    function _power(uint256 base, uint256 precision, uint256 exponent) internal pure returns (uint256 result) {
        if (exponent == 0) {
            return precision; // base^0 = 1 (with precision)
        }

        result = precision; // Start with 1 (with precision)
        uint256 currentBase = base;
        uint256 currentExponent = exponent;

        // Use binary exponentiation for efficiency
        while (currentExponent > 0) {
            if (currentExponent % 2 == 1) {
                result = (result * currentBase) / precision;
            }
            currentBase = (currentBase * currentBase) / precision;
            currentExponent = currentExponent / 2;
        }

        return result;
    }

    /**
     * @notice Distribute rewards to ai agent and creator
     * @dev Only callable by whitelisted distributors, once per interval per group
     * @param _coin IPCoin address
     * @param amount Amount to distribute
     */
    function distributeCreatorRewards(address _coin, uint256 amount) external whenNotPaused nonReentrant {
        // Check if caller is whitelisted
        require(creatorRewardDistributorsWhitelist[msg.sender], "Not authorized");

        CoinPoolData storage poolData = coinPoolsData[_coin];

        // Check if IPCoin exists
        require(address(poolData.ipCoinContract) != address(0), CoinNotExists());

        // Check if time interval has passed for this group (uses coin-specific interval if set, otherwise uses coin's default)
        uint256 interval = poolData.specificRewardInterval > 0
            ? poolData.specificRewardInterval
            : poolData.parameters.defaultRewardInterval;
        require(
            block.timestamp > poolData.lastCreatorDistributionTimestamp + interval
                || poolData.lastCreatorDistributionTimestamp == 0,
            "Time interval limit: one distribution per interval"
        );

        // Check if mining has been exhausted
        require(poolData.remainingSupply > 0, "Mining supply exhausted");

        // Handle legacy data migration - if creationTimestamp is not set or too small (possibly legacy data)
        if (poolData.creationTimestamp <= 100) {
            poolData.creationTimestamp = block.timestamp;
        }

        // Get current week number using the dedicated method
        uint256 currentWeekNumber = this.getCurrentWeekNumber(_coin);

        // 计算最大允许输出（基于指数衰减算法）
        // 使用正确的指数衰减公式：Rw = R1 × (1-k)^(w-1)
        uint256 maxIntervalOutput = this.getWeeklyOutputLimit(_coin, currentWeekNumber);

        // Ensure we don't distribute more than what's available in remaining supply
        if (maxIntervalOutput > poolData.remainingSupply) {
            maxIntervalOutput = poolData.remainingSupply;
        }

        // Make sure distribution amount doesn't exceed the calculated maximum
        require(amount <= maxIntervalOutput, "Exceeds maximum weekly production rate");

        // Check if total distribution exceeds epoch limit
        uint256 currentEpochStart = block.timestamp - (block.timestamp % interval);

        // Reset epoch total if we're in a new epoch
        if (currentEpochStart > poolData.lastCreatorDistributionTimestamp) {
            poolData.epochCreatorRewardDistributionTotal = 0;
        }

        // Update epoch total and cumulative distribution
        poolData.epochCreatorRewardDistributionTotal += amount;
        poolData.cumulativeDistribution += amount;

        // 更新剩余供应量
        // Sw = Sw-1 - Rw (remaining supply = previous remaining supply - current week reward)
        poolData.remainingSupply -= amount;

        // 更新最后分配时间戳
        poolData.lastCreatorDistributionTimestamp = block.timestamp;

        // 获取创建者地址 - 通过NFT tokenId 0的持有者
        address creator;
        try poolData.creatorNftContract.ownerOf(0) returns (address owner) {
            creator = owner;
        } catch {
            revert();
        }

        // 按照规则分配代币：60%给创建者，20%给平台，20%给AI钱包
        uint256 creatorAmount = (amount * 60) / 100; // 60% 创建者
        uint256 platformAmount = (amount * 20) / 100; // 20% 平台
        uint256 aiWalletAmount = amount - creatorAmount - platformAmount; // 20% AI钱包

        bool success;
        success = poolData.ipCoinContract.transfer(creator, creatorAmount);
        require(success, "Transfer failed");
        success = poolData.ipCoinContract.transfer(FEE_RECEIVER, platformAmount);
        require(success, "Transfer failed");
        success = poolData.ipCoinContract.transfer(poolData.aiWallet, aiWalletAmount);
        require(success, "Transfer failed");

        emit CreatorRewardsDistributed(msg.sender, _coin, amount);
        emit WeeklyMiningReward(_coin, currentWeekNumber, amount, poolData.remainingSupply);
    }

    // todo: 引入一个 epoch 或者 era 的概念，在该方法 emit event 时把 epoch/era number 也写到链上
    /**
     * @notice Distribute rewards to multiple addresses
     * @dev Only callable by whitelisted distributors, once per interval per group
     * @dev Limited to a maximum of MAX_WALLET_DISTRIBUTION_PERCENTAGE of current wallet balance
     * @param _coin IPCoin address
     * @param distributions Array of reward distributions (max 10 recipients)
     */
    function distributeRewards(
        address _coin,
        RewardDistribution[] calldata distributions
    ) external whenNotPaused nonReentrant {
        CoinPoolData storage poolData = coinPoolsData[_coin];

        // Check if IPCoin exists
        require(address(poolData.ipCoinContract) != address(0), CoinNotExists());

        // Check if caller is whitelisted
        require(poolData.aiWallet == msg.sender, "Not authorized");

        // Check maximum number of recipients
        require(distributions.length <= 10, "Too many recipients");

        // Calculate total distribution amount
        uint256 totalDistribution = 0;
        for (uint256 i = 0; i < distributions.length; i++) {
            totalDistribution += distributions[i].amount;
        }

        // Define the distribution interval, used for periodic limits like interval total
        uint256 interval = poolData.specificRewardInterval > 0
            ? poolData.specificRewardInterval
            : poolData.parameters.defaultRewardInterval;

        // Check if total distribution exceeds interval limit
        uint256 currentEpochStart = block.timestamp - (block.timestamp % interval);

        // Reset interval total if we're in a new interval
        if (currentEpochStart > poolData.lastDistributionTimestamp) {
            poolData.intervalRewardDistributionTotal = 0;
        }

        // Check if total distribution exceeds maximum percentage of wallet balance
        uint256 currentWalletBalance = poolData.ipCoinContract.balanceOf(msg.sender);
        uint256 maxDistributionAmount =
            (currentWalletBalance * poolData.parameters.maxWalletDistributionPercentage) / 10_000; // Convert basis points to percentage

        require(totalDistribution < maxDistributionAmount, "Exceeds maximum wallet distribution percentage");

        // Update interval total
        poolData.intervalRewardDistributionTotal += totalDistribution;

        // Update last distribution timestamp
        poolData.lastDistributionTimestamp = block.timestamp;

        // Distribute rewards
        for (uint256 i = 0; i < distributions.length; i++) {
            require(distributions[i].amount > 0, "Invalid amount");

            // Check if recipient has already received reward in this interval
            require(
                lastRewardReceivedTimestamp[_coin][distributions[i].recipient] < currentEpochStart,
                RecipientRewardedThisInterval()
            );

            poolData.ipCoinContract.transferFrom(msg.sender, distributions[i].recipient, distributions[i].amount);

            // Update the last reward received timestamp for the recipient
            lastRewardReceivedTimestamp[_coin][distributions[i].recipient] = block.timestamp;
        }

        emit RewardsDistributed(msg.sender, _coin, totalDistribution, distributions.length);
    }

    /**
     * @notice Set common parameters used by both initialize and migrateNewVersion functions
     * @dev This internal function sets parameters that are common to both functions
     */
    function _setCommonParameters() internal {
        LIQUIDITY_TOKEN_AMOUNT = 100_000_000 * DEFAULT_PRECISION;
        BONDING_CURVE_TRADE_CAP = 400_000_000 * DEFAULT_PRECISION;
        TRADE_FEE_RATIO = 3 * 1e16; // 3%
        MINIMUM_ETH_TO_BUY = 0.0001 ether;
        currentVersion = 2_025_060_501;

        // Exponential decay mining model - constants are defined as immutable above
        // Set default maximum distribution to 10% of wallet balance
        MAX_WALLET_DISTRIBUTION_PERCENTAGE = 1000; // 10% in basis points
        // Set default global reward interval to 1 week
        globalRewardInterval = 1 weeks;

        // Exponential decay mining parameters
        TOTAL_MINING_SUPPLY = 500_000_000 * DEFAULT_PRECISION; // 500M tokens
        DECAY_CONSTANT = 5530; // k = 0.00553 represented as 5530/1000000
        DECAY_PRECISION = 1_000_000; // Precision for decay constant
        INITIAL_WEEKLY_REWARD = 2_765_000 * DEFAULT_PRECISION; // R1 = k × S0
    }

    /**
     * @notice Set chain-specific parameters based on the current blockchain
     * @dev This internal function sets parameters specific to each supported blockchain
     */
    function _setChainSpecificParameters() internal {
        if (block.chainid == 8453 || block.chainid == 84_532) {
            // base mainnet or base sepolia
            LIQUIDITY_NATIVE_AMOUNT = 6.4 ether; // ~6.6 eth(6.59xx) --> 100%
            VIRTUAL_ETH_SUPPLY = 0.1494 ether;
            VIRTUAL_TOKEN_SUPPLY = 106_774_000 * DEFAULT_PRECISION;
            PRICE_CONSTANT = 7_240_000_000; // k 7.24 × 10^-9 with 18 decimal places
        } else if (block.chainid == 97 || block.chainid == 56) {
            // bsc testnet or bsc mainnet
            LIQUIDITY_NATIVE_AMOUNT = 12 ether; // 12.36 bnb -> 12.7308 bnb
            VIRTUAL_ETH_SUPPLY = 0.06 ether;
            VIRTUAL_TOKEN_SUPPLY = 29_505_700 * DEFAULT_PRECISION;
            PRICE_CONSTANT = 16_396_300_000; // k 1.63963×10^-8 with 18 decimal places
        } else {
            revert UnsupportedChain();
        }
    }

    receive() external payable {}

    fallback() external payable {}

    // The following functions are overrides required by Solidity.
    function _authorizeUpgrade(
        address newImplementation
    ) internal override onlyRole(ADMIN_ROLE) {}
}
