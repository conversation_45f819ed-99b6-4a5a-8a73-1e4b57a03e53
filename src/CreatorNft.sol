// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

import "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/token/ERC721/ERC721Upgradeable.sol";
import "@openzeppelin/contracts-upgradeable/token/ERC721/extensions/ERC721URIStorageUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/token/ERC721/extensions/ERC721BurnableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "./interfaces/IPool.sol";

contract CreatorNft is
    Initializable,
    ERC721Upgradeable,
    UUPSUpgradeable,
    ERC721URIStorageUpgradeable,
    OwnableUpgradeable,
    ERC721BurnableUpgradeable
{
    uint256 private nextTokenId;
    bool public paused;
    IPool public pool;

    error MintingPaused();

    modifier whenNotPaused() {
        require(!paused, MintingPaused());
        _;
    }

    modifier onlyPoolAdmin() {
        if (!checkAdmin(_msgSender())) revert OwnableUnauthorizedAccount(_msgSender());
        _;
    }

    /// @custom:oz-upgrades-unsafe-allow constructor
    constructor() {
        _disableInitializers();
    }

    function initialize(
        address initialOwner,
        address creator,
        address poolAddress,
        string memory name_,
        string memory symbol_,
        string memory uri
    ) public initializer {
        // Zero address checks
        require(initialOwner != address(0), "Initial owner cannot be zero address");
        require(creator != address(0), "Creator cannot be zero address");
        require(poolAddress != address(0), "Pool address cannot be zero address");

        __ERC721_init(name_, symbol_);
        __ERC721URIStorage_init();
        __Ownable_init(initialOwner);
        __ERC721Burnable_init();

        pool = IPool(poolAddress);

        safeMint(creator, uri);

        paused = true;
    }

    function safeMint(address to, string memory uri) public whenNotPaused {
        require(to != address(0), "Cannot mint to zero address");

        uint256 tokenId = nextTokenId++;
        // Set token URI before external call to prevent reentrancy issues
        _setTokenURI(tokenId, uri);
        _safeMint(to, tokenId);
    }

    function updateTokenUri(uint256 tokenId, string memory uri) external onlyPoolAdmin {
        _setTokenURI(tokenId, uri);
    }

    function changePaused(
        bool pausedState
    ) external onlyPoolAdmin {
        paused = pausedState;
    }

    function setPool(
        address newPool
    ) public onlyPoolAdmin {
        // Check if the pool address is valid and its checkAdmin() function exists
        requirePoolAddressIsValid(newPool);
        pool = IPool(newPool);
    }

    function checkAdmin(
        address account
    ) internal view returns (bool) {
        if (address(pool) == address(0) && account == owner()) {
            return true;
        }
        return pool.checkAdmin(account);
    }

    function requirePoolAddressIsValid(
        address poolToValidate
    ) internal view {
        // Check if the pool address is a contract
        require(poolToValidate.code.length > 0, "Pool address is not a contract");

        // Use try-catch to safely call the interface function
        try IPool(poolToValidate).checkAdmin(_msgSender()) returns (bool isAdmin) {
            require(isAdmin, "The caller must be an admin of the pool");
        } catch {
            revert("Invalid pool address or checkAdmin function failed");
        }
    }

    // The following functions are overrides required by Solidity.
    function _authorizeUpgrade(
        address newImplementation
    ) internal override onlyPoolAdmin {}

    function _update(
        address to,
        uint256 tokenId,
        address auth
    ) internal override(ERC721Upgradeable) returns (address) {
        return super._update(to, tokenId, auth);
    }

    function tokenURI(
        uint256 tokenId
    ) public view override(ERC721Upgradeable, ERC721URIStorageUpgradeable) returns (string memory) {
        return super.tokenURI(tokenId);
    }

    function supportsInterface(
        bytes4 interfaceId
    ) public view override(ERC721Upgradeable, ERC721URIStorageUpgradeable) returns (bool) {
        return super.supportsInterface(interfaceId);
    }
}
