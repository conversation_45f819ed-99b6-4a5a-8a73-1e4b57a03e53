// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

import "forge-std/Test.sol";
import "forge-std/console.sol";
import "../src/Factory.sol";
import "../src/IPCoin.sol";
import "../src/TraderNft.sol";
import "../src/CreatorNft.sol";
import "../src/Governance.sol";
import "../src/DistributionPool.sol";
import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/proxy/ERC1967/ERC1967Proxy.sol";

contract MockUSDT is ERC20 {
    constructor() ERC20("Mock USDT", "USDT") {}

    function mint(address to, uint256 amount) public {
        _mint(to, amount);
    }
}

contract FactoryTest is Test {
    Factory public factory;

    address public owner;
    address public feeCollector;

    function setUp() public {
        console.log("Starting setUp");

        owner = address(this);
        feeCollector = address(this);

        console.log("Deploying implementation contracts");
        address creatorNftImpl = address(new CreatorNft());

        console.log("Deploying DistributionPool proxy");
        address poolProxy = address(new ERC1967Proxy(address(new DistributionPool()), ""));

        console.log("Deploying Factory proxy");
        address factoryProxy = address(new ERC1967Proxy(address(new Factory()), ""));
        factory = Factory(factoryProxy);
        factory.initialize(owner, creatorNftImpl, poolProxy);

        DistributionPool pool = DistributionPool(payable(poolProxy));
        pool.initialize(owner, owner, factoryProxy);

        console.log("Factory proxy deployed at", factoryProxy);

        console.log("setUp completed");
    }

    function testDeployIP() public {
        console.log("Testing deployIP");
        Factory.DeploymentParams memory params = Factory.DeploymentParams({
            coinName: "Test Coin",
            coinSymbol: "TC",
            traderNftName: "Test Trader NFT",
            traderNftSymbol: "TTN",
            traderNftBaseURI: "https://test-uri.com/",
            creator: address(******************************************),
            aiAgentWallet: address(******************************************),
            creatorNftName: "Test Creator NFT",
            creatorNftSymbol: "TCN",
            creatorNftURI: "https://test-creator-uri.com/token.json",
            ipRef: "0x123"
        });

        vm.expectRevert(Factory.TraderNftNotDeployed.selector);
        factory.deployIP(params);
    }

    function testDeployIPWithoutTraderNft() public {
        console.log("Testing deployIP");

        // Create a proper EOA address for testing
        address creator = vm.addr(1); // This creates a proper EOA address

        Factory.DeploymentWithOutTraderNftParams memory params = Factory.DeploymentWithOutTraderNftParams({
            coinName: "Test Coin",
            coinSymbol: "TC",
            creator: creator,
            aiAgentWallet: creator,
            creatorNftName: "Test Creator NFT",
            creatorNftSymbol: "TCN",
            creatorNftURI: "https://test-creator-uri.com/token.json",
            ipRef: "00"
        });

        factory.deployIPWithoutTraderNft{value: 0.1 ether}(params);
    }
}
