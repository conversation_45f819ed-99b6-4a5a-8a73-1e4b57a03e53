// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

import "../src/DistributionPool.sol";
import "../src/interfaces/IDistributionPool.sol";
import "../src/IPCoin.sol";
import "../src/TraderNft.sol";
import "../src/CreatorNft.sol";
import "../src/Governance.sol";
import "../src/Factory.sol";
import "@openzeppelin/contracts/proxy/ERC1967/ERC1967Proxy.sol";
import "forge-std/Test.sol";
import "forge-std/console.sol";

contract DistributionPoolTest is Test {
    DistributionPool public pool;
    address public poolProxy;
    IPCoin public coin;
    TraderNft public nft;
    CreatorNft public creatorNft;
    address public owner;
    address public user1;
    address public user2;
    address public feeReceiver;
    address public creator;
    address public attacker;
    address public aiWallet;
    address public UNISWAP_ROUTER;
    uint256 public bigBuyAmount;

    function setUp() public {
        console.log("Starting setUp");
        owner = address(this);
        user1 = address(0x1);
        user2 = address(0x2);
        feeReceiver = address(0x3);
        attacker = address(0x3);
        aiWallet = address(0x4);
        creator = address(0x5);

        if (block.chainid == 8453) {
            // base mainnet
            UNISWAP_ROUTER = ******************************************;
            bigBuyAmount = 6.6 ether;
        } else if (block.chainid == 84_532) {
            // base sepolia
            UNISWAP_ROUTER = ******************************************;
            bigBuyAmount = 6.6 ether;
        } else if (block.chainid == 97) {
            // bsc testnet
            UNISWAP_ROUTER = ******************************************;
            bigBuyAmount = 12.753746414 ether;
        } else if (block.chainid == 56) {
            // bsc mainnet
            bigBuyAmount = 12.753746414 ether;
            UNISWAP_ROUTER = ******************************************;
        }

        console.log("Dealing ETH to users");
        // send eth to test users
        vm.deal(user1, 100 ether);
        vm.deal(user2, 100 ether);
        vm.deal(aiWallet, 1 ether);

        console.log("Deploying DistributionPool");
        DistributionPool poolImpl = new DistributionPool();
        poolProxy = address(new ERC1967Proxy(address(poolImpl), ""));
        pool = DistributionPool(payable(poolProxy));

        console.log("Deploying CreatorNft implementation");
        CreatorNft creatorNftImpl = new CreatorNft();

        console.log("Deploying Factory");
        Factory factoryImpl = new Factory();
        address factoryProxy = address(new ERC1967Proxy(address(factoryImpl), ""));
        Factory factory = Factory(factoryProxy);
        factory.initialize(owner, address(creatorNftImpl), poolProxy);

        pool.initialize(owner, feeReceiver, factoryProxy); // Initialize DistributionPool first

        console.log("Deploying IP via Factory");
        Factory.DeploymentWithOutTraderNftParams memory params = Factory.DeploymentWithOutTraderNftParams({
            coinName: "IP Coin",
            coinSymbol: "IPC",
            creator: creator,
            aiAgentWallet: aiWallet,
            creatorNftName: "Creator NFT",
            creatorNftSymbol: "CNFT",
            creatorNftURI: "https://ipnft.io/creator/1",
            ipRef: "testIPRef"
        });

        // // Listener for IPDeployed event to get deployed contract addresses
        // vm.expectEmit(true, true, true, true);
        // emit Factory.IPDeployed(creator, "testIPRef", address(0), address(0), address(0)); // Values will be checked by the actual event
        coin = IPCoin(factory.deployIPWithoutTraderNft(params));

        // Retrieve deployed contract addresses from event (This is a common pattern in Forge tests)
        // However, for simplicity in this modification, we'll assume the factory correctly deploys and we can get the addresses
        // In a real scenario, you'd capture the event and extract addresses.
        // For now, we will need to query the factory or assume a fixed deployment pattern if events are hard to capture directly in this setup.
        // Let's assume the factory's internal logic correctly sets up the IPCoin and CreatorNft that DistributionPool will use.
        // The addIp function in DistributionPool is called internally by the factory during deployIPWithoutTraderNft.
        // So, we don't need to call pool.addIp explicitly here if the factory handles it.

        // We need to get the deployed IPCoin and CreatorNft addresses to interact with them in tests.
        // This part might require adjustments based on how Factory emits events or stores deployed addresses.
        // For now, let's assume we can't easily get them back here without more complex event handling or factory view functions.
        // The tests below might need to be adapted if they directly use `coin` or `creatorNft` variables that were previously set up.
        // For the purpose of this modification, we will comment out direct usage if it's not straightforward to get the new instances.

        // coin = IPCoin(address(factory.getLatestIPCoin())); // Example: if factory had such a getter
        // creatorNft = CreatorNft(address(factory.getLatestCreatorNft())); // Example: if factory had such a getter

        console.log("IP deployment via factory completed. addIp is called within deployIPWithoutTraderNft.");

        console.log("setUp completed");
    }

    function testBuy() public {
        uint256 ethAmount = 0.1189 ether;
        uint256 initialBalance = user1.balance;

        uint256 expectedTokens = pool.calculateTokenAmount(address(coin), ethAmount);

        console.log("calculateTokenAmount Expected tokens:", expectedTokens);

        vm.startPrank(user1);
        pool.buy{value: ethAmount}(address(coin), user1, expectedTokens - 100);
        vm.stopPrank();

        // Assert ETH transfers happened correctly
        assertEq(user1.balance, initialBalance - ethAmount, "ETH should be deducted from user");
        assertEq(address(pool).balance, ethAmount, "Pool should receive ETH");

        // Assert token minting happened correctly
        assertEq(coin.balanceOf(user1), expectedTokens, "User should receive correct token amount");
    }

    function testSell() public {
        // First, buy some tokens
        uint256 ethAmount = 0.01 ether;
        uint256 expectedTokens = pool.calculateTokenAmount(address(coin), ethAmount);

        console.log("Expected buy tokens:", expectedTokens);

        // Buy tokens
        vm.startPrank(user1);
        pool.buy{value: ethAmount}(address(coin), user1, expectedTokens - 1);
        vm.stopPrank();

        // Get initial balances before selling
        uint256 initialCoinBalance = coin.balanceOf(user1);
        uint256 initialEthBalance = user1.balance;

        console.log("Initial coin balance:", initialCoinBalance);
        console.log("Initial ETH balance:", initialEthBalance);

        // Calculate expected ETH return
        uint256 expectedEth = pool.calculateEthAmount(address(coin), 1e12);
        console.log("1e12 Expected ETH return:", expectedEth);
        expectedEth = pool.calculateEthAmount(address(coin), 1e13);
        console.log("1e13 Expected ETH return:", expectedEth);
        expectedEth = pool.calculateEthAmount(address(coin), 1e14);
        console.log("1e14 Expected ETH return:", expectedEth);
        expectedEth = pool.calculateEthAmount(address(coin), 1e15);
        console.log("1e15 Expected ETH return:", expectedEth);
        expectedEth = pool.calculateEthAmount(address(coin), 1e16);
        console.log("1e16 Expected ETH return:", expectedEth);
        expectedEth = pool.calculateEthAmount(address(coin), 1e17);
        console.log("1e17 Expected ETH return:", expectedEth);
        expectedEth = pool.calculateEthAmount(address(coin), 1e18);
        console.log("1e18 Expected ETH return:", expectedEth);
        expectedEth = pool.calculateEthAmount(address(coin), 1e19);
        console.log("1e19 Expected ETH return:", expectedEth);

        expectedEth = pool.calculateEthAmount(address(coin), initialCoinBalance);
        console.log("initialCoinBalance Expected ETH return:", expectedEth);

        // Approve and sell tokens
        vm.startPrank(user1);
        coin.approve(address(pool), initialCoinBalance);
        pool.sell(address(coin), initialCoinBalance, 0);
        vm.stopPrank();

        // Assert the results
        assertGt(user1.balance, initialEthBalance, "User should receive ETH");
        assertEq(coin.balanceOf(user1), 0, "User should have no tokens left");

        console.log("Final ETH balance:", user1.balance);
        console.log("Final coin balance:", coin.balanceOf(user1));
    }

    function testSmoothInitialPricing() public {
        uint256 ethAmount = 0.1 ether;
        vm.startPrank(user1);
        uint256 tokenAmount1 = pool.calculateTokenAmount(address(coin), ethAmount);
        console.log("tokenAmount1:", tokenAmount1);
        pool.buy{value: ethAmount}(address(coin), user1, tokenAmount1 - 100);

        uint256 tokenAmount2 = pool.calculateTokenAmount(address(coin), ethAmount);
        console.log("tokenAmount2:", tokenAmount1);
        pool.buy{value: ethAmount}(address(coin), user1, tokenAmount2 - 100);

        uint256 tokenAmount3 = pool.calculateTokenAmount(address(coin), ethAmount);
        console.log("tokenAmount3:", tokenAmount1);
        pool.buy{value: ethAmount}(address(coin), user1, tokenAmount3 - 100);

        vm.stopPrank();
        assertLt(tokenAmount2, tokenAmount1, "Second purchase should yield fewer tokens");
        assertLt(tokenAmount3, tokenAmount2, "Second purchase should yield fewer tokens");
    }

    function test_RevertWhen_BuyingWithInsufficientEth() public {
        uint256 ethAmount = 0.00001 ether; // Less than minimum required
        vm.expectRevert(IDistributionPool.MinimumTransactionLimit.selector);
        vm.prank(user1);
        pool.buy{value: ethAmount}(address(coin), user1, 0);
    }

    function testWithdrawFee() public {
        // First make some purchases to generate fees
        uint256 ethAmount = 1 ether;

        vm.startPrank(user1);
        pool.buy{value: ethAmount}(address(coin), user1, 0);
        vm.stopPrank();

        uint256 initialFeeReceiverBalance = feeReceiver.balance;

        // Withdraw fees
        pool.withdrawFee();

        assertGt(feeReceiver.balance, initialFeeReceiverBalance, "Fee receiver should receive fees");
    }

    function testPause() public {
        pool.pause();

        uint256 ethAmount = 1 ether;
        vm.startPrank(user1);
        // Expect revert with specific reason
        vm.expectRevert();
        pool.buy{value: ethAmount}(address(coin), user1, 0);
        vm.stopPrank();

        pool.unpause();

        vm.startPrank(user1);
        pool.buy{value: ethAmount}(address(coin), user1, 0);
        vm.stopPrank();
    }

    function testMaxTokenCalculation() public view {
        uint256[] memory testAmounts = new uint256[](6);
        testAmounts[0] = 1 ether;
        testAmounts[1] = 1.5 ether;
        testAmounts[2] = 1.9 ether;
        testAmounts[3] = 2 ether;
        testAmounts[4] = 2.1 ether;
        testAmounts[5] = 2.5 ether;

        for (uint256 i = 0; i < testAmounts.length; i++) {
            uint256 tokens = pool.calculateTokenAmount(address(coin), testAmounts[i]);
            console.log("ETH Amount:", testAmounts[i] / 1e17, "Tokens:", tokens);
        }
    }

    function testDexTrading() public {
        // First add sufficient liquidity to create the pool
        uint256 buyAmount = bigBuyAmount;
        vm.startPrank(user1);
        uint256 expectedTokens = pool.calculateTokenAmount(address(coin), buyAmount);
        pool.buy{value: buyAmount}(address(coin), user1, expectedTokens - 100);
        vm.stopPrank();

        IUniswapV2Router02 router = IUniswapV2Router02(UNISWAP_ROUTER);

        // Test swap ETH for tokens
        vm.startPrank(user2);
        uint256 initialTokenBalance = coin.balanceOf(user2);

        // Approve router to spend tokens
        coin.approve(UNISWAP_ROUTER, type(uint256).max);

        // Swap 0.1 ETH for tokens
        uint256 swapAmount = 0.1 ether;
        address[] memory path = new address[](2);
        path[0] = router.WETH();
        path[1] = address(coin);

        router.swapExactETHForTokens{value: swapAmount}(
            0, // Accept any amount of tokens
            path,
            user2,
            block.timestamp + 300
        );

        uint256 finalTokenBalance = coin.balanceOf(user2);
        assertGt(finalTokenBalance, initialTokenBalance, "Should receive tokens from swap");
        console.log("Token initialTokenBalance:", initialTokenBalance);
        console.log("Token finalTokenBalance:", finalTokenBalance);

        // Test swap tokens for ETH
        uint256 tokenSwapAmount = finalTokenBalance / 2;
        uint256 initialEthBalance = user2.balance;

        path[0] = address(coin);
        path[1] = router.WETH();

        router.swapExactTokensForETH(
            tokenSwapAmount,
            0, // Accept any amount of ETH
            path,
            user2,
            block.timestamp + 300
        );

        assertGt(user2.balance, initialEthBalance, "Should receive ETH from swap");

        console.log("ETH initialEthBalance:", initialEthBalance);
        console.log("ETH finalEthBalance:", user2.balance);

        assertLt(coin.balanceOf(user2), finalTokenBalance, "Token balance should decrease");
        vm.stopPrank();
    }

    function testAddAdmin() public {
        address newAdmin = address(0x4);

        // Only admin can add new admin
        vm.prank(user1);
        vm.expectRevert();
        pool.addAdmin(newAdmin);

        // Admin can add new admin
        pool.addAdmin(newAdmin);
        assertTrue(pool.hasRole(pool.ADMIN_ROLE(), newAdmin));
    }

    function testRemoveAdmin() public {
        address newAdmin = address(0x4);
        pool.addAdmin(newAdmin);

        // Only admin can remove admin
        vm.prank(user1);
        vm.expectRevert();
        pool.removeAdmin(newAdmin);

        // Cannot remove zero address
        vm.expectRevert("Invalid admin address");
        pool.removeAdmin(address(0));

        // Cannot remove self
        vm.prank(newAdmin);
        vm.expectRevert("You cannot remove yourself");
        pool.removeAdmin(newAdmin);

        // Admin can remove other admin
        pool.removeAdmin(newAdmin);
        assertFalse(pool.hasRole(pool.ADMIN_ROLE(), newAdmin));
    }

    function testAddPauser() public {
        address newPauser = address(0x4);

        // Only admin can add new pauser
        vm.prank(user1);
        vm.expectRevert();
        pool.addPauser(newPauser);

        // Admin can add new pauser
        pool.addPauser(newPauser);
        assertTrue(pool.hasRole(pool.PAUSER_ROLE(), newPauser));
    }

    function testRemovePauser() public {
        address newPauser = address(0x4);
        pool.addPauser(newPauser);

        // Only admin can remove pauser
        vm.prank(user1);
        vm.expectRevert();
        pool.removePauser(newPauser);

        // Admin can remove pauser
        pool.removePauser(newPauser);
        assertFalse(pool.hasRole(pool.PAUSER_ROLE(), newPauser));
    }

    function testMinimumBuyAmount() public {
        // Test buying with minimum allowed amount
        uint256 minAmount = pool.MINIMUM_ETH_TO_BUY();

        vm.startPrank(user1);
        // Should succeed with minimum amount
        uint256 expectedTokens = pool.calculateTokenAmount(address(coin), minAmount);
        pool.buy{value: minAmount}(address(coin), user1, expectedTokens - 1);

        // Should fail with amount less than minimum
        uint256 tooSmallAmount = minAmount - 1;
        vm.expectRevert(IDistributionPool.MinimumTransactionLimit.selector);
        pool.buy{value: tooSmallAmount}(address(coin), user1, 0);
        vm.stopPrank();
    }

    function testContinuousBuySell() public {
        uint256 initialBalance = user1.balance;
        uint256 ethAmount = 0.1 ether;

        vm.startPrank(user1);

        // First buy
        uint256 buyTokens = pool.calculateTokenAmount(address(coin), ethAmount);
        pool.buy{value: ethAmount}(address(coin), user1, buyTokens - 1);

        // Immediate sell of all tokens
        uint256 tokenBalance = coin.balanceOf(user1);
        coin.approve(address(pool), tokenBalance);
        pool.sell(address(coin), tokenBalance, 0);

        // Final balance should be less than initial due to fees
        uint256 finalBalance = user1.balance;
        console.log("Initial balance:", initialBalance);
        console.log("Final balance:", finalBalance);
        assertTrue(finalBalance < initialBalance, "User should not profit from continuous buy-sell");
        assertTrue(initialBalance - finalBalance > 0.005 ether, "Fee impact should be significant");

        vm.stopPrank();
    }

    function testLargeTradeSlippage() public {
        uint256 largeAmount = 2 ether;

        vm.startPrank(user1);

        // Calculate expected tokens for different amounts
        uint256 smallBuyTokens = pool.calculateTokenAmount(address(coin), 0.1 ether);
        uint256 largeBuyTokens = pool.calculateTokenAmount(address(coin), largeAmount);

        // Verify price impact
        uint256 smallPrice = (0.1 ether * 1e18) / smallBuyTokens;
        uint256 largePrice = (largeAmount * 1e18) / largeBuyTokens;
        assertTrue(largePrice > smallPrice, "Large trades should have worse prices due to slippage");

        // Test actual large buy
        pool.buy{value: largeAmount}(address(coin), user1, largeBuyTokens - 1);

        vm.stopPrank();
    }

    function testPriceManipulationResistance() public {
        vm.startPrank(user1);

        // Try to manipulate price with multiple small trades
        uint256 smallAmount = 0.01 ether;
        uint256[] memory prices = new uint256[](5);

        for (uint256 i = 0; i < 5; i++) {
            uint256 tokens = pool.calculateTokenAmount(address(coin), smallAmount);
            pool.buy{value: smallAmount}(address(coin), user1, tokens - 1);
            prices[i] = (smallAmount * 1e18) / tokens;

            // Small sell to try to manipulate
            if (i > 0) {
                uint256 sellAmount = coin.balanceOf(user1) / 2;
                coin.approve(address(pool), sellAmount);
                pool.sell(address(coin), sellAmount, 0);
            }
        }

        // Verify price stability
        for (uint256 i = 1; i < 5; i++) {
            uint256 priceDiff = prices[i] > prices[i - 1] ? prices[i] - prices[i - 1] : prices[i - 1] - prices[i];
            assertTrue(priceDiff < prices[i] / 10, "Price should not change dramatically");
        }

        vm.stopPrank();
    }

    function testFeeCalculationAccuracy() public {
        uint256 buyAmount = 1 ether;
        uint256 initialFeeReceiver = feeReceiver.balance;

        vm.startPrank(user1);

        // Buy tokens
        uint256 tokens = pool.calculateTokenAmount(address(coin), buyAmount);
        pool.buy{value: buyAmount}(address(coin), user1, tokens - 1);

        // Get fee after buy
        uint256 buyFee = pool.getTotalEthFee();
        uint256 expectedBuyFee = (buyAmount * 3) / 100;
        assertApproxEqAbs(buyFee, expectedBuyFee, 1e15, "Buy fee calculation should be accurate");
        console.log("Buy amount:", buyAmount);
        console.log("Expected buy fee:", expectedBuyFee);
        console.log("Actual buy fee:", buyFee);

        // Sell all tokens
        uint256 sellEthAmount = pool.calculateEthAmount(address(coin), tokens);
        coin.approve(address(pool), tokens);
        pool.sell(address(coin), tokens, 0);

        // Get total fee after sell
        uint256 totalFee = pool.getTotalEthFee();
        uint256 sellFee = totalFee - buyFee;
        uint256 expectedSellFee = ((1 ether - buyFee) * 3) / 100;
        assertApproxEqAbs(sellFee, expectedSellFee, 1e15, "Sell fee calculation should be accurate");
        console.log("Sell amount:", sellEthAmount);
        console.log("Expected sell fee:", expectedSellFee);
        console.log("Actual sell fee:", sellFee);

        vm.stopPrank();

        // Test non-admin cannot withdraw fee
        vm.prank(user1);
        vm.expectRevert();
        pool.withdrawFee();

        // Test admin can withdraw fee
        vm.prank(owner);
        pool.withdrawFee();

        // Verify complete withdrawal
        assertEq(pool.getTotalEthFee(), 0, "All fees should be withdrawn");
        assertEq(feeReceiver.balance - initialFeeReceiver, totalFee, "Fee receiver should receive correct total fees");

        // Test withdrawal with empty balance
        vm.prank(owner);
        vm.expectRevert();
        pool.withdrawFee();
    }

    function testMultipleTradesFeeAccuracy() public {
        uint256 initialFeeReceiver = feeReceiver.balance;
        uint256 totalExpectedFee = 0;

        vm.startPrank(user1);

        // Perform multiple rounds of buy and sell
        uint256[] memory tradeAmounts = new uint256[](3);
        tradeAmounts[0] = 0.5 ether;
        tradeAmounts[1] = 0.3 ether;
        tradeAmounts[2] = 0.7 ether;

        for (uint256 i = 0; i < tradeAmounts.length; i++) {
            uint256 buyAmount = tradeAmounts[i];
            console.log("\nRound", i + 1);
            console.log("Buy amount:", buyAmount);

            // Record fee before buy
            uint256 feeBeforeBuy = pool.getTotalEthFee();

            // Buy tokens
            uint256 tokens = pool.calculateTokenAmount(address(coin), buyAmount);
            pool.buy{value: buyAmount}(address(coin), user1, tokens - 1);

            // Calculate and verify buy fee
            uint256 actualBuyFee = pool.getTotalEthFee() - feeBeforeBuy;
            uint256 expectedBuyFee = (buyAmount * 3) / 100;
            assertApproxEqAbs(actualBuyFee, expectedBuyFee, 1e15, "Buy fee calculation should be accurate");
            console.log("Buy fee expected:", expectedBuyFee);
            console.log("Buy fee actual:", actualBuyFee);

            totalExpectedFee += expectedBuyFee;

            // Record fee before sell
            uint256 feeBeforeSell = pool.getTotalEthFee();

            // Sell all tokens
            uint256 sellEthAmount = pool.calculateEthAmount(address(coin), tokens);
            coin.approve(address(pool), tokens);
            pool.sell(address(coin), tokens, 0);

            // Calculate and verify sell fee
            uint256 actualSellFee = pool.getTotalEthFee() - feeBeforeSell;
            // Use the actual sellEthAmount (before fees) to calculate expected sell fee
            uint256 rawSellEthAmount = sellEthAmount + actualSellFee; // Add back the fee to get raw amount
            uint256 expectedSellFee = (rawSellEthAmount * 3) / 100;
            assertApproxEqAbs(actualSellFee, expectedSellFee, 1e15, "Sell fee calculation should be accurate");
            console.log("Sell fee expected:", expectedSellFee);
            console.log("Sell fee actual:", actualSellFee);

            totalExpectedFee += expectedSellFee;
        }

        vm.stopPrank();

        // Verify total accumulated fees
        uint256 totalActualFee = pool.getTotalEthFee();
        console.log("\nTotal Results:");
        console.log("Total expected fee:", totalExpectedFee);
        console.log("Total actual fee:", totalActualFee);
        assertApproxEqAbs(totalActualFee, totalExpectedFee, 1e15, "Total fee calculation should be accurate");

        // Withdraw fees and verify
        vm.prank(owner);
        pool.withdrawFee();

        assertEq(pool.getTotalEthFee(), 0, "All fees should be withdrawn");
        assertEq(
            feeReceiver.balance - initialFeeReceiver, totalActualFee, "Fee receiver should receive correct total fees"
        );

        // Calculate maximum fee difference percentage
        uint256 feeDiff =
            totalActualFee > totalExpectedFee ? totalActualFee - totalExpectedFee : totalExpectedFee - totalActualFee;
        uint256 feeErrorPercentage = (feeDiff * 1e18) / totalExpectedFee;
        console.log("Fee difference:", feeDiff);
        console.log("Fee error percentage:", feeErrorPercentage, "/ 1e18 (0.1%)");
        assertTrue(feeErrorPercentage <= 1e15, "Fee error should be less than 0.1%");
    }

    function testAutoLiquidityPoolCreation() public {
        uint256 requiredEthAmount = 1 ether;

        // Buy tokens until max cap is reached
        vm.startPrank(user1);
        uint256 expectedTokens = pool.calculateTokenAmount(address(coin), requiredEthAmount);

        console.log("1 ether buy expectedTokens:", expectedTokens);

        pool.buy{value: requiredEthAmount}(address(coin), user1, expectedTokens - 100);
        vm.stopPrank();

        // Verify trading is disabled and liquidity pool is created automatically
        assertTrue(pool.getCoinTradeStatus(address(coin)), "Trading should be enabled");

        requiredEthAmount = bigBuyAmount;
        expectedTokens = pool.calculateTokenAmount(address(coin), requiredEthAmount);

        console.log("bigBuyAmount expectedTokens:", expectedTokens);

        pool.buy{value: requiredEthAmount}(address(coin), user1, expectedTokens - 100);
        vm.stopPrank();

        // Verify trading is disabled and liquidity pool is created automatically
        assertFalse(pool.getCoinTradeStatus(address(coin)), "Trading should be disabled");

        // Try to buy after pool creation - should fail
        vm.startPrank(user1);
        vm.expectRevert(IDistributionPool.FeatureDisabled.selector);
        pool.buy{value: 0.1 ether}(address(coin), user1, 0);
        vm.stopPrank();
    }

    function testBuyAfterListing() public {
        // First create liquidity pool
        uint256 requiredEthAmount = bigBuyAmount;

        vm.startPrank(user1);
        uint256 expectedTokens = pool.calculateTokenAmount(address(coin), requiredEthAmount);
        console.log("expectedTokens:", expectedTokens);
        pool.buy{value: requiredEthAmount}(address(coin), user1, expectedTokens - 100);
        vm.stopPrank();

        // Try to buy after listing - should fail
        vm.startPrank(user2);
        console.log("Buy after listing");
        vm.expectRevert(IDistributionPool.FeatureDisabled.selector);
        pool.buy{value: 0.1 ether}(address(coin), user2, 0);
        vm.stopPrank();
    }

    function testSellAfterListing() public {
        // First buy some tokens
        uint256 ethAmount = 0.1 ether;
        vm.startPrank(user1);
        uint256 expectedTokens = pool.calculateTokenAmount(address(coin), ethAmount);
        pool.buy{value: ethAmount}(address(coin), user1, expectedTokens - 100);
        vm.stopPrank();

        // Create liquidity pool
        uint256 requiredEthAmount = bigBuyAmount;
        vm.startPrank(user2);
        expectedTokens = pool.calculateTokenAmount(address(coin), requiredEthAmount);
        pool.buy{value: requiredEthAmount}(address(coin), user2, expectedTokens - 100);
        vm.stopPrank();

        // Try to sell after listing - should fail
        vm.startPrank(user1);
        // vm.expectRevert(IDistributionPool.FeatureDisabled.selector);
        vm.expectRevert();
        pool.sell(address(coin), ethAmount, 0);
        vm.stopPrank();
    }

    // Since we don't have direct getter functions for the AI wallet and creator NFT,
    // we'll verify them indirectly by checking features that use them
    function testAiWalletAndCreatorNft() public {
        // Setup for the distribution tests
        // Need to mint some tokens to the pool for distribution
        uint256 distributionAmount = 1000 * 1e18;

        // Add user1 to the whitelist for creator reward distribution
        vm.prank(owner);
        pool.addRewardDistributor(user1);

        // Distribute creator rewards - this implicitly uses the CreatorNft
        vm.prank(user1);
        pool.distributeCreatorRewards(address(coin), distributionAmount);

        // If no errors are thrown, it indicates the CreatorNft was set correctly
        // We'd need more comprehensive testing with mocks to fully verify this
        assertTrue(true, "Distribution through CreatorNft completed successfully");
    }

    // Test version control
    function testVersionControl() public {
        // Check initial version
        uint256 version = pool.currentVersion();
        assertEq(version, 2_025_060_501, "Initial version should be 2025060501");

        // Verify IP version
        (,,,,,,,,, uint256 ipVersion,,,,,,,,,) = pool.coinPoolsData(address(coin));
        console.log("ipVersion:", ipVersion);

        // Migrate to new version
        vm.prank(owner);
        vm.expectRevert();
        pool.migrateNewVersion();

        // Version should remain 1 since we're already at version 1
        version = pool.currentVersion();
        assertEq(version, 2_025_060_501, "Version should still be 2025060501");
    }

    // Test reward distribution whitelist
    function testRewardDistributorWhitelist() public {
        // Initially not whitelisted
        bool isWhitelisted = pool.creatorRewardDistributorsWhitelist(user1);
        assertFalse(isWhitelisted, "User should not be whitelisted initially");

        // Add to whitelist
        vm.prank(owner);
        pool.addRewardDistributor(user1);

        // Verify whitelisted
        isWhitelisted = pool.creatorRewardDistributorsWhitelist(user1);
        assertTrue(isWhitelisted, "User should be whitelisted");

        // Remove from whitelist
        vm.prank(owner);
        pool.removeRewardDistributor(user1);

        // Verify removed from whitelist
        isWhitelisted = pool.creatorRewardDistributorsWhitelist(user1);
        assertFalse(isWhitelisted, "User should be removed from whitelist");
    }

    // Test setting max wallet distribution percentage
    function testSetMaxWalletDistributionPercentage() public {
        // Set new percentage
        uint256 newPercentage = 2000; // 20% in basis points
        vm.prank(owner);
        pool.setMaxWalletDistributionPercentage(newPercentage);

        // Verify new percentage
        assertEq(
            pool.MAX_WALLET_DISTRIBUTION_PERCENTAGE(),
            newPercentage,
            "Max wallet distribution percentage should be updated"
        );
    }

    // Test creator reward distribution
    function testCreatorRewardDistribution() public {
        // Need to mint some tokens to the pool for distribution
        uint256 distributionAmount = 1000 * 1e18;

        // Add user1 to the whitelist
        vm.prank(owner);
        pool.addRewardDistributor(user1);

        // Record initial balances
        uint256 initialBalance = coin.balanceOf(creator);

        // Calculate expected amount after distribution
        uint256 expectedBalance = initialBalance + (distributionAmount * 60) / 100;
        console.log("expectedBalance:", expectedBalance);
        // Distribute rewards
        vm.prank(user1);
        pool.distributeCreatorRewards(address(coin), distributionAmount);

        // Verify balance update
        uint256 finalBalance = coin.balanceOf(creator);
        assertEq(finalBalance, expectedBalance, "Recipient should receive the correct tokens amount");

        // Check that weekly distribution is tracked
        (,,,,,,,,,,, uint256 weeklyTotal,,,,,,,) = pool.coinPoolsData(address(coin));
        assertEq(weeklyTotal, distributionAmount, "Weekly distribution total should be tracked");
    }

    // Test reward distribution batching
    function testBatchRewardDistribution() public {
        // Add user1 to the whitelist for creator reward distribution
        vm.prank(owner);
        pool.addRewardDistributor(user1);

        // Set a specific interval for this test to check RecipientRewardedThisInterval
        vm.prank(owner);
        uint256 testInterval = 1; // << CHANGED to 1
        pool.setRewardInterval(address(coin), testInterval);

        // Calculate total amount needed for distribution
        uint256 totalDistributionAmount = 6000 * 1e18; // 1000 + 2000 + 3000

        // First, distribute tokens to aiWallet using distributeCreatorRewards
        // This will give aiWallet enough tokens to distribute later
        vm.prank(user1);
        pool.distributeCreatorRewards(address(coin), totalDistributionAmount);

        // Verify aiWallet received the tokens
        assertEq(coin.balanceOf(aiWallet), (totalDistributionAmount * 20) / 100, "AI wallet should receive tokens");

        uint256 aiWalletBalance = coin.balanceOf(aiWallet);

        // Create distribution batch using RewardDistribution struct
        DistributionPool.RewardDistribution[] memory distributions = new DistributionPool.RewardDistribution[](3);
        distributions[0] = DistributionPool.RewardDistribution({recipient: user1, amount: 10 * 1e18});
        distributions[1] = DistributionPool.RewardDistribution({recipient: user2, amount: 20 * 1e18});
        distributions[2] = DistributionPool.RewardDistribution({recipient: owner, amount: 30 * 1e18});

        // Record initial balances
        uint256 initialBalance1 = coin.balanceOf(user1);
        uint256 initialBalance2 = coin.balanceOf(user2);
        uint256 initialBalance3 = coin.balanceOf(owner);

        // aiWallet needs to approve the pool to spend its tokens
        vm.prank(aiWallet);
        coin.approve(address(pool), totalDistributionAmount);

        // Distribute rewards using the AI wallet
        vm.prank(aiWallet);
        pool.distributeRewards(address(coin), distributions);

        // Verify balance updates
        assertEq(
            coin.balanceOf(user1),
            initialBalance1 + distributions[0].amount,
            "Recipient 1 should receive correct amount"
        );
        assertEq(
            coin.balanceOf(user2),
            initialBalance2 + distributions[1].amount,
            "Recipient 2 should receive correct amount"
        );
        assertEq(
            coin.balanceOf(owner),
            initialBalance3 + distributions[2].amount,
            "Recipient 3 should receive correct amount"
        );

        // Verify aiWallet's tokens were spent
        assertEq(coin.balanceOf(aiWallet), aiWalletBalance - 60 * 1e18, "AI wallet should have spent all its tokens");

        // Check that interval distribution is tracked
        // uint256 intervalTotal = pool.intervalRewardDistributionTotal(address(coin));
        // assertEq(
        //     intervalTotal,
        //     distributions[0].amount + distributions[1].amount + distributions[2].amount,
        //     "Interval distribution total should be tracked"
        // );

        // ---- ADDED PART: Test RecipientRewardedThisInterval ----
        // aiWallet has already approved enough tokens

        // Try to distribute to user1 (distributions[0].recipient) again immediately - should fail
        DistributionPool.RewardDistribution[] memory singleDistToUser1 = new DistributionPool.RewardDistribution[](1);
        singleDistToUser1[0] = DistributionPool.RewardDistribution({recipient: user1, amount: 1 * 1e18});

        vm.prank(aiWallet);
        vm.expectRevert();
        pool.distributeRewards(address(coin), singleDistToUser1);

        // Also try to distribute the whole batch again - should fail on the first recipient (user1)
        vm.prank(aiWallet);
        vm.expectRevert();
        pool.distributeRewards(address(coin), distributions);

        // Advance time by a small amount (1 second) to simulate a new transaction/block
        // With interval = 1, currentEpochStart will be the new block.timestamp
        vm.warp(block.timestamp + 1); // << CHANGED from testInterval + 1

        // Try to distribute to user1 again - should succeed as block.timestamp has advanced
        uint256 user1BalanceBeforeRetry = coin.balanceOf(user1);
        vm.prank(aiWallet);
        pool.distributeRewards(address(coin), singleDistToUser1);
        assertEq(
            coin.balanceOf(user1),
            user1BalanceBeforeRetry + singleDistToUser1[0].amount,
            "User1 should receive reward after interval"
        );

        // The intervalRewardDistributionTotal should have reset because a new interval (effectively a new block/timestamp) has started.
        // lastDistributionTimestamp[address(coin)] was updated by the first successful batch call from aiWallet.
        // currentEpochStart (which is new block.timestamp) for this new call by aiWallet will be > lastDistributionTimestamp[address(coin)].
        (,,,,,,,,,, uint256 newIntervalTotal,,,,,,,,) = pool.coinPoolsData(address(coin));
        assertEq(
            newIntervalTotal,
            singleDistToUser1[0].amount,
            "Interval total should reset for new transaction and track new distribution"
        );
    }

    // Test creator rewards distribution functionality with exponential decay model
    function testWeeklyRewardLimit() public {
        uint256 initialRewardsAmount = 500 * 1e18;

        // Calculate initial maximum weekly output based on exponential decay
        // Use the new getWeeklyOutputLimit method for accurate calculation
        uint256 currentWeekNumber = pool.getCurrentWeekNumber(address(coin));
        uint256 maxWeeklyOutput = pool.getWeeklyOutputLimit(address(coin), currentWeekNumber);

        // Also get the parameters for comparison
        uint256 totalMiningSupply = pool.TOTAL_MINING_SUPPLY();
        uint256 decayConstant = pool.DECAY_CONSTANT();
        uint256 decayPrecision = pool.DECAY_PRECISION();

        // For week 1, the output should be R1 = k * S0
        uint256 expectedFirstWeekOutput = (totalMiningSupply * decayConstant) / decayPrecision;
        assertEq(maxWeeklyOutput, expectedFirstWeekOutput, "First week output should match R1 = k * S0");

        // 记录初始余额
        uint256 initialCreatorBalance = coin.balanceOf(creator);
        uint256 initialPlatformBalance = coin.balanceOf(feeReceiver);
        uint256 initialAiWalletBalance = coin.balanceOf(aiWallet);

        // 测试1: 确认非白名单用户无法调用该方法
        vm.prank(user2);
        vm.expectRevert("Not authorized");
        pool.distributeCreatorRewards(address(coin), initialRewardsAmount);

        // 添加user1到白名单
        vm.prank(owner);
        pool.addRewardDistributor(user1);

        // 测试2: 使用无效币种应该失败
        vm.prank(user1);
        vm.expectRevert(bytes4(keccak256("CoinNotExists()"))); // 使用自定义错误
        pool.distributeCreatorRewards(address(0x123), initialRewardsAmount);

        // 测试3: 验证奖励分配和每周限制
        // 第一次分配应该成功
        vm.prank(user1);
        pool.distributeCreatorRewards(address(coin), initialRewardsAmount);

        // 验证每周总额正确记录
        (,,,,,,,,,,, uint256 weeklyTotal,,,,,,,) = pool.coinPoolsData(address(coin));
        assertEq(weeklyTotal, initialRewardsAmount, "Weekly distribution total should match");

        // 验证累计分配额增加
        (,,,,,,,,,,,,,, uint256 cumulativeDistribution,,,,) = pool.coinPoolsData(address(coin));
        assertEq(cumulativeDistribution, initialRewardsAmount, "Cumulative distribution should match");

        // 验证资金分配正确 (60% 创作者, 20% 平台, 20% AI钱包)
        uint256 expectedCreatorAmount = (initialRewardsAmount * 60) / 100;
        uint256 expectedPlatformAmount = (initialRewardsAmount * 20) / 100;
        uint256 expectedAiWalletAmount = initialRewardsAmount - expectedCreatorAmount - expectedPlatformAmount;

        assertEq(
            coin.balanceOf(creator),
            initialCreatorBalance + expectedCreatorAmount,
            "Creator should receive 60% of rewards"
        );
        assertEq(
            coin.balanceOf(feeReceiver),
            initialPlatformBalance + expectedPlatformAmount,
            "Platform should receive 20% of rewards"
        );
        assertEq(
            coin.balanceOf(aiWallet),
            initialAiWalletBalance + expectedAiWalletAmount,
            "AI wallet should receive 20% of rewards"
        );

        // 测试4: 同一周内尝试再次分配应该失败
        vm.prank(user1);
        vm.expectRevert("Time interval limit: one distribution per interval");
        pool.distributeCreatorRewards(address(coin), 1);

        // 记录上次分配时间
        (,,,,,,,,,,,,, uint256 lastDistributionTime,,,,,) = pool.coinPoolsData(address(coin));
        console.log("Last distribution timestamp before warp:", lastDistributionTime);

        // 前进时间（确保超过一周）
        uint256 timeBeforeWarp = block.timestamp;
        console.log("Current timestamp before warp:", timeBeforeWarp);

        // 直接前进确切的一周+1秒，确保满足时间条件
        vm.warp(lastDistributionTime + 1 weeks + 1);

        uint256 timeAfterWarp = block.timestamp;
        console.log("Current timestamp after warp:", timeAfterWarp);
        console.log("Time difference (should be > 1 week):", timeAfterWarp - lastDistributionTime);

        // 测试5: 超出最大周产出限制应该失败
        // 获取当前周的最大产出限制
        uint256 currentWeekAfterWarp = pool.getCurrentWeekNumber(address(coin));
        uint256 currentMaxOutput = pool.getWeeklyOutputLimit(address(coin), currentWeekAfterWarp);

        vm.prank(user1);
        vm.expectRevert("Exceeds maximum weekly production rate");
        pool.distributeCreatorRewards(address(coin), currentMaxOutput + 1);

        // 测试6: 成功的第二次分配（不同周）
        uint256 secondDistributionAmount = 400 * 1e18;

        // 记录第二次分配前的余额
        uint256 secondInitialCreatorBalance = coin.balanceOf(creator);
        uint256 secondInitialPlatformBalance = coin.balanceOf(feeReceiver);
        uint256 secondInitialAiWalletBalance = coin.balanceOf(aiWallet);

        // 进行第二次分配
        vm.prank(user1);
        pool.distributeCreatorRewards(address(coin), secondDistributionAmount);

        // 验证每周总额被重置且累计值增加
        (,,,,,,,,,,, weeklyTotal,,,,,,,) = pool.coinPoolsData(address(coin));
        assertEq(weeklyTotal, secondDistributionAmount, "Weekly total should be reset for new week");

        (,,,,,,,,,,,,,, cumulativeDistribution,,,,) = pool.coinPoolsData(address(coin));
        assertEq(
            cumulativeDistribution,
            initialRewardsAmount + secondDistributionAmount,
            "Cumulative distribution should increase"
        );

        // 验证第二次分配的资金分配
        uint256 secondExpectedCreatorAmount = (secondDistributionAmount * 60) / 100;
        uint256 secondExpectedPlatformAmount = (secondDistributionAmount * 20) / 100;
        uint256 secondExpectedAiWalletAmount =
            secondDistributionAmount - secondExpectedCreatorAmount - secondExpectedPlatformAmount;

        assertEq(
            coin.balanceOf(creator),
            secondInitialCreatorBalance + secondExpectedCreatorAmount,
            "Creator should receive 60% of second rewards"
        );
        assertEq(
            coin.balanceOf(feeReceiver),
            secondInitialPlatformBalance + secondExpectedPlatformAmount,
            "Platform should receive 20% of second rewards"
        );
        assertEq(
            coin.balanceOf(aiWallet),
            secondInitialAiWalletBalance + secondExpectedAiWalletAmount,
            "AI wallet should receive 20% of second rewards"
        );

        // 测试7: 验证指数衰减机制
        // 更新上次分配时间
        (,,,,,,,,,,,,, lastDistributionTime,,,,,) = pool.coinPoolsData(address(coin));
        console.log("Last distribution timestamp before decay test:", lastDistributionTime);

        // 前进到另一周（确切地说是 上次分配时间 + 1周 + 1秒）
        vm.warp(lastDistributionTime + 1 weeks + 1);
        console.log("Timestamp after another warp:", block.timestamp);

        // 获取当前剩余供应量
        (,,,,,,,,,,,,,, uint256 newCumulativeDistribution, uint256 remainingSupply,,,) =
            pool.coinPoolsData(address(coin));
        console.log("Current cumulative distribution:", newCumulativeDistribution);
        console.log("Current remaining supply:", remainingSupply);

        // 计算当前最大周产出（使用正确的指数衰减公式）
        uint256 currentWeek = pool.getCurrentWeekNumber(address(coin));
        uint256 currentMaxWeeklyOutput = pool.getWeeklyOutputLimit(address(coin), currentWeek);
        console.log("Current week number:", currentWeek);
        console.log("Current max weekly output:", currentMaxWeeklyOutput);

        // 测试指数衰减：剩余供应量应该减少，最大产出也应该减少
        uint256 thirdDistributionAmount = 300 * 1e18;

        // 进行第三次分配
        vm.prank(user1);
        pool.distributeCreatorRewards(address(coin), thirdDistributionAmount);

        // 验证剩余供应量减少了
        (,,,,,,,,,,,,,, uint256 finalCumulativeDistribution, uint256 finalRemainingSupply,,,) =
            pool.coinPoolsData(address(coin));

        assertEq(
            finalRemainingSupply,
            remainingSupply - thirdDistributionAmount,
            "Remaining supply should decrease by distribution amount"
        );
        // Note: Week number is now calculated based on creation timestamp, not incremented
        // So we don't test for simple increment anymore
        assertEq(
            finalCumulativeDistribution,
            newCumulativeDistribution + thirdDistributionAmount,
            "Cumulative distribution should increase"
        );

        // 验证新的最大周产出更小（指数衰减）
        uint256 newCurrentWeek = pool.getCurrentWeekNumber(address(coin));
        uint256 newMaxWeeklyOutput = pool.getWeeklyOutputLimit(address(coin), newCurrentWeek);
        console.log("New week number:", newCurrentWeek);
        console.log("New max weekly output after decay:", newMaxWeeklyOutput);

        // The weekly output should decrease due to exponential decay (higher week number = lower output)
        if (newCurrentWeek > currentWeek) {
            assertLt(
                newMaxWeeklyOutput, currentMaxWeeklyOutput, "Max weekly output should decrease due to exponential decay"
            );
        }

        // 前进时间并测试新的限制 - 跳跃更多时间确保到达下一周
        (,,,,,,,,,,,,, uint256 finalLastDistributionTime,,,,,) = pool.coinPoolsData(address(coin));
        vm.warp(finalLastDistributionTime + 2 weeks + 1);

        // 获取新周的最大产出限制
        uint256 finalWeekNumber = pool.getCurrentWeekNumber(address(coin));
        uint256 finalMaxWeeklyOutput = pool.getWeeklyOutputLimit(address(coin), finalWeekNumber);
        console.log("Final week number:", finalWeekNumber);
        console.log("Final max weekly output:", finalMaxWeeklyOutput);

        // 尝试分配超过新限制的金额应该失败
        vm.prank(user1);
        vm.expectRevert("Exceeds maximum weekly production rate");
        pool.distributeCreatorRewards(address(coin), finalMaxWeeklyOutput + 1);

        // 分配在新限制内的金额应该成功
        vm.prank(user1);
        pool.distributeCreatorRewards(address(coin), finalMaxWeeklyOutput);

        console.log("Exponential decay test completed successfully");
    }

    // Test week number calculation based on creation timestamp
    function testWeekNumberCalculation() public {
        // Add user1 to whitelist
        vm.prank(owner);
        pool.addRewardDistributor(user1);

        // Test using the existing coin from setUp
        // Verify creation timestamp is set for the existing coin
        (,,,,,,,,,,,,,,,, uint256 creationTimestamp,,) = pool.coinPoolsData(address(coin));
        assertGt(creationTimestamp, 0, "Creation timestamp should be set");

        // Test distribution works with the existing coin
        vm.prank(user1);
        pool.distributeCreatorRewards(address(coin), 100 * 1e18);

        // Test that we can't distribute again immediately (same week)
        vm.prank(user1);
        vm.expectRevert("Time interval limit: one distribution per interval");
        pool.distributeCreatorRewards(address(coin), 100 * 1e18);

        // Move forward by one week to test week transition
        vm.warp(block.timestamp + 1 weeks + 1);

        // Should allow distribution in the next week
        vm.prank(user1);
        pool.distributeCreatorRewards(address(coin), 100 * 1e18);

        console.log("Week number calculation test completed successfully");
    }

    // Test week transitions and calendar week logic
    function testWeekTransitions() public {
        // Add user1 to whitelist
        vm.prank(owner);
        pool.addRewardDistributor(user1);

        // Use existing coin and test week transitions
        // First distribution
        vm.prank(user1);
        pool.distributeCreatorRewards(address(coin), 100 * 1e18);

        // Should not allow another distribution in the same interval
        vm.prank(user1);
        vm.expectRevert("Time interval limit: one distribution per interval");
        pool.distributeCreatorRewards(address(coin), 100 * 1e18);

        // Move forward by more than the interval (1 week + 1 second)
        // This ensures both the calendar week logic and interval check pass
        vm.warp(block.timestamp + 1 weeks + 1);

        // Should allow distribution after the interval has passed
        vm.prank(user1);
        pool.distributeCreatorRewards(address(coin), 100 * 1e18);

        console.log("Week transition test completed successfully");
    }

    // Test legacy data migration
    function testLegacyDataMigration() public {
        // Add user1 to whitelist
        vm.prank(owner);
        pool.addRewardDistributor(user1);

        // Test that the existing coin has a proper creation timestamp
        (,,,,,,,,,,,,,,,, uint256 normalTimestamp,,) = pool.coinPoolsData(address(coin));
        assertGt(normalTimestamp, 100, "Normal creation timestamp should be greater than 100");

        // Test that distribution works normally with proper timestamp
        vm.prank(user1);
        pool.distributeCreatorRewards(address(coin), 100 * 1e18);

        // The legacy migration logic is tested implicitly:
        // - If creationTimestamp <= 100, it gets set to block.timestamp
        // - This ensures old data gets migrated automatically
        // - Since our test coin has a proper timestamp, no migration is needed

        console.log("Legacy data migration test completed successfully");
    }

    // Test specific week number calculation scenarios
    function testSpecificWeekNumberScenarios() public {
        // Add user1 to whitelist
        vm.prank(owner);
        pool.addRewardDistributor(user1);

        // Test multiple distributions over several weeks using existing coin
        // First distribution
        vm.prank(user1);
        pool.distributeCreatorRewards(address(coin), 100 * 1e18);

        // Move forward 2 weeks to test week calculation
        vm.warp(block.timestamp + 2 weeks + 1);

        // Should allow distribution after 2 weeks
        vm.prank(user1);
        pool.distributeCreatorRewards(address(coin), 100 * 1e18);

        // Move forward 3 weeks
        vm.warp(block.timestamp + 3 weeks + 1);

        // Should allow distribution after 3 more weeks
        vm.prank(user1);
        pool.distributeCreatorRewards(address(coin), 100 * 1e18);

        console.log("Specific week number scenarios test completed successfully");
    }

    // Test edge cases for week calculation
    function testWeekCalculationEdgeCases() public {
        // Add user1 to whitelist
        vm.prank(owner);
        pool.addRewardDistributor(user1);

        // Test basic distribution functionality
        vm.prank(user1);
        pool.distributeCreatorRewards(address(coin), 100 * 1e18);

        // Should not allow another distribution immediately
        vm.prank(user1);
        vm.expectRevert("Time interval limit: one distribution per interval");
        pool.distributeCreatorRewards(address(coin), 100 * 1e18);

        // Move forward by exactly the interval (1 week)
        vm.warp(block.timestamp + 1 weeks);

        // Should still be blocked because we need > interval
        vm.prank(user1);
        vm.expectRevert("Time interval limit: one distribution per interval");
        pool.distributeCreatorRewards(address(coin), 100 * 1e18);

        // Move forward by one more second
        vm.warp(block.timestamp + 1);

        // Should be allowed now
        vm.prank(user1);
        pool.distributeCreatorRewards(address(coin), 100 * 1e18);

        console.log("Week calculation edge cases test completed successfully");
    }

    // Test setting mining interval functionality
    function testSetRewardInterval() public {
        // Only admin can set mining interval
        vm.prank(user1);
        vm.expectRevert();
        pool.setRewardInterval(address(coin), 1 days);

        // Admin sets mining interval to 1 day
        vm.prank(owner);
        pool.setRewardInterval(address(coin), 1 days);

        // Verify interval was set correctly
        (,,,,,,,,,,,,,,,,, uint256 rewardInterval,) = pool.coinPoolsData(address(coin));
        assertEq(rewardInterval, 1 days, "Reward interval should be set to 1 day");

        // Setting to zero is now allowed (means use global interval)
        vm.prank(owner);
        pool.setRewardInterval(address(coin), 0);
        (,,,,,,,,,,,,,,,,, rewardInterval,) = pool.coinPoolsData(address(coin));
        assertEq(rewardInterval, 0, "Reward interval should be set to 0");

        // Test with invalid coin address should revert
        vm.prank(owner);
        vm.expectRevert(bytes4(keccak256("CoinNotExists()")));
        pool.setRewardInterval(address(0x123), 1 days);
    }

    // Test setting global mining interval
    function testSetGlobalRewardInterval() public {
        // Verify default global interval is 1 week
        assertEq(pool.globalRewardInterval(), 1 weeks, "Default global mining interval should be 1 week");

        // Only admin can set global mining interval
        vm.prank(user1);
        vm.expectRevert();
        pool.setGlobalRewardInterval(1 days);

        // Admin sets global mining interval to 1 day
        vm.prank(owner);
        pool.setGlobalRewardInterval(1 days);

        // Verify global interval was set correctly
        assertEq(pool.globalRewardInterval(), 1 days, "Global mining interval should be set to 1 day");

        // Test invalid interval (zero) should revert
        vm.prank(owner);
        vm.expectRevert("Interval must be greater than zero");
        pool.setGlobalRewardInterval(0);
    }

    // Test distribution with custom mining interval
    function testCustomIntervalDistribution() public {
        // Add user1 to whitelist
        vm.prank(owner);
        pool.addRewardDistributor(user1);

        // Set mining interval to 1 day
        vm.prank(owner);
        pool.setRewardInterval(address(coin), 1 days);

        // First distribution
        vm.prank(user1);
        pool.distributeCreatorRewards(address(coin), 500 * 1e18);

        // Trying to distribute again immediately should fail
        vm.prank(user1);
        vm.expectRevert();
        pool.distributeCreatorRewards(address(coin), 500 * 1e18);

        // Advance time by just under 1 day - should still fail
        vm.warp(block.timestamp + 1 days - 10);
        vm.prank(user1);
        vm.expectRevert();
        pool.distributeCreatorRewards(address(coin), 500 * 1e18);

        // Advance time by 1 day + 1 second - should succeed
        vm.warp(block.timestamp + 11);
        vm.prank(user1);
        pool.distributeCreatorRewards(address(coin), 500 * 1e18);

        // Test AI wallet distribution with custom interval too
        // Set up AI wallet
        vm.prank(aiWallet);
        coin.approve(address(pool), 1000 * 1e18);

        // Create distributions
        DistributionPool.RewardDistribution[] memory distributions = new DistributionPool.RewardDistribution[](1);
        distributions[0] = DistributionPool.RewardDistribution({recipient: user1, amount: 10 * 1e18});

        // First AI distribution
        vm.prank(aiWallet);
        pool.distributeRewards(address(coin), distributions);

        // Trying again immediately should fail
        vm.prank(aiWallet);
        vm.expectRevert();
        pool.distributeRewards(address(coin), distributions);

        // Advance time by 1 day + 1 second - should succeed
        vm.warp(block.timestamp + 1 days + 1);
        vm.prank(aiWallet);
        pool.distributeRewards(address(coin), distributions);
    }

    // Test global interval fallback when coin's interval is 0
    function testGlobalIntervalFallback() public {
        // Add user1 to whitelist
        vm.prank(owner);
        pool.addRewardDistributor(user1);

        // Set coin's default reward interval to 2 days (simulating the effect of global interval)
        vm.prank(owner);
        pool.setCoinDefaultRewardInterval(address(coin), 2 days);

        // Set coin's specific mining interval to 0 (use coin's default)
        vm.prank(owner);
        pool.setRewardInterval(address(coin), 0);

        // First distribution
        vm.prank(user1);
        pool.distributeCreatorRewards(address(coin), 500 * 1e18);

        // Advance time by 1 day - should still fail (coin's default interval is 2 days)
        vm.warp(block.timestamp + 1 days);
        vm.prank(user1);
        vm.expectRevert();
        pool.distributeCreatorRewards(address(coin), 500 * 1e18);

        // Advance time to 2 days + 1 second - should succeed
        vm.warp(block.timestamp + 1 days + 1);
        vm.prank(user1);
        pool.distributeCreatorRewards(address(coin), 500 * 1e18);

        // Now test that coin-specific interval takes precedence
        // Set coin's default interval to 3 days
        vm.prank(owner);
        pool.setCoinDefaultRewardInterval(address(coin), 3 days);

        // Set coin's specific interval to 12 hours
        vm.prank(owner);
        pool.setRewardInterval(address(coin), 12 hours);

        // Should be blocked still
        vm.prank(user1);
        vm.expectRevert();
        pool.distributeCreatorRewards(address(coin), 500 * 1e18);

        // Advance just past 12 hours - should succeed
        vm.warp(block.timestamp + 12 hours + 1);
        vm.prank(user1);
        pool.distributeCreatorRewards(address(coin), 500 * 1e18);
    }

    // Test the new getWeeklyOutputLimit method
    function testGetWeeklyOutputLimit() public view {
        // Test week 1 output
        uint256 week1Output = pool.getWeeklyOutputLimit(address(coin), 1);
        console.log("Week 1 output:", week1Output);

        // Test week 2 output
        uint256 week2Output = pool.getWeeklyOutputLimit(address(coin), 2);
        console.log("Week 2 output:", week2Output);

        // Test week 3 output
        uint256 week3Output = pool.getWeeklyOutputLimit(address(coin), 3);
        console.log("Week 3 output:", week3Output);

        // Verify that outputs decrease (exponential decay)
        assertGt(week1Output, week2Output, "Week 1 output should be greater than week 2");
        assertGt(week2Output, week3Output, "Week 2 output should be greater than week 3");

        // Verify week 1 output matches expected R1 = k * S0
        uint256 totalMiningSupply = pool.TOTAL_MINING_SUPPLY();
        uint256 decayConstant = pool.DECAY_CONSTANT();
        uint256 decayPrecision = pool.DECAY_PRECISION();
        uint256 expectedWeek1 = (totalMiningSupply * decayConstant) / decayPrecision;

        assertEq(week1Output, expectedWeek1, "Week 1 output should match R1 = k * S0");

        console.log("Expected week 1 output:", expectedWeek1);
        console.log("Actual week 1 output:", week1Output);
    }

    receive() external payable {}
}
